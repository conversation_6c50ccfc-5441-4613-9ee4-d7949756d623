#!/usr/bin/env node

/**
 * 综合测试脚本 - 整合所有测试功能
 * 包含API接口测试、数据结构验证、服务器监控、登录功能测试等
 *
 * 使用方法：
 * node comprehensive_test.js [--mode=api|monitor|login|all] [--env=prod|dev] [--timeout=30] [--retry=3] [--verbose]
 *
 * 模式说明：
 * - api: API接口测试和数据结构验证
 * - monitor: 服务器可用性监控
 * - login: 登录功能测试
 * - all: 运行所有测试（默认）
 */

const https = require('https');
const fs = require('fs');
const path = require('path');

// 命令行参数解析
const args = process.argv.slice(2);
const config = {
    mode: 'all',
    env: 'prod',
    timeout: 30,
    retry: 3,
    verbose: false
};

args.forEach(arg => {
    if (arg.startsWith('--mode=')) {
        config.mode = arg.split('=')[1];
    } else if (arg.startsWith('--env=')) {
        config.env = arg.split('=')[1];
    } else if (arg.startsWith('--timeout=')) {
        config.timeout = parseInt(arg.split('=')[1]);
    } else if (arg.startsWith('--retry=')) {
        config.retry = parseInt(arg.split('=')[1]);
    } else if (arg === '--verbose') {
        config.verbose = true;
    }
});

// 服务器配置 - 已调整主备服务器
const SERVERS = {
    primary: 'ncm.zhenxin.me',
    backup: '1355831898-4499wupl9z.ap-guangzhou.tencentscf.com'
};

// 环境配置
const ENVIRONMENTS = {
    prod: SERVERS,
    dev: SERVERS
};

// API接口配置
const API_ENDPOINTS = {
    // 搜索相关
    cloudsearch: {
        path: '/cloudsearch?keywords=%E5%91%A8%E6%9D%B0%E4%BC%A6&type=1&limit=1',
        expectedFields: ['code', 'result'],
        description: '云搜索接口',
        critical: true
    },
    searchSuggest: {
        path: '/search/suggest?keywords=%E5%91%A8%E6%9D%B0%E4%BC%A6&type=mobile',
        expectedFields: ['code', 'result'],
        description: '搜索建议接口',
        critical: false
    },

    // 歌曲相关
    songDetail: {
        path: '/song/detail?ids=347230',
        expectedFields: ['code', 'songs'],
        description: '歌曲详情接口',
        critical: true
    },
    songUrl: {
        path: '/song/url?id=347230&br=320000',
        expectedFields: ['code', 'data'],
        description: '歌曲播放链接接口',
        critical: true
    },
    lyric: {
        path: '/lyric?id=347230',
        expectedFields: ['code', 'lrc'],
        description: '歌词接口',
        critical: false
    },

    // 用户相关
    loginStatus: {
        path: '/login/status',
        expectedFields: ['data', 'data.code'],
        description: '登录状态检查',
        critical: false
    },
    userAccount: {
        path: '/user/account',
        expectedFields: ['code'],
        description: '用户账号信息',
        critical: false
    },

    // 推荐相关
    recommend: {
        path: '/recommend/songs',
        expectedFields: ['code'],
        description: '推荐歌曲接口',
        critical: false
    },
    banner: {
        path: '/banner?type=1',
        expectedFields: ['code', 'banners'],
        description: '轮播图接口',
        critical: false
    }
};

// 测试结果存储
let testResults = {
    timestamp: new Date().toISOString(),
    environment: config.env,
    servers: ENVIRONMENTS[config.env],
    mode: config.mode,
    summary: {
        total: 0,
        passed: 0,
        failed: 0,
        critical_failed: 0,
        primary_available: 0,
        backup_available: 0,
        success_rate: 0,
        primary_rate: 0,
        backup_rate: 0
    },
    api_tests: {},
    monitor_results: {},
    login_tests: {}
};

// 颜色输出（CI环境兼容）
const colors = {
    green: (text) => process.env.CI ? text : `\x1b[32m${text}\x1b[0m`,
    red: (text) => process.env.CI ? text : `\x1b[31m${text}\x1b[0m`,
    yellow: (text) => process.env.CI ? text : `\x1b[33m${text}\x1b[0m`,
    blue: (text) => process.env.CI ? text : `\x1b[34m${text}\x1b[0m`,
    cyan: (text) => process.env.CI ? text : `\x1b[36m${text}\x1b[0m`,
    bold: (text) => process.env.CI ? text : `\x1b[1m${text}\x1b[0m`,
    dim: (text) => process.env.CI ? text : `\x1b[2m${text}\x1b[0m`
};

// 日志函数
function log(level, message, details = null) {
    const timestamp = new Date().toISOString();
    const prefix = `[${timestamp}] [${level.toUpperCase()}]`;

    switch (level) {
        case 'info':
            console.log(colors.blue(prefix), message);
            break;
        case 'warn':
            console.warn(colors.yellow(prefix), message);
            break;
        case 'error':
            console.error(colors.red(prefix), message);
            break;
        case 'success':
            console.log(colors.green(prefix), message);
            break;
        case 'debug':
            if (config.verbose) {
                console.log(colors.dim(prefix), message);
            }
            break;
        default:
            console.log(prefix, message);
    }

    if (details && config.verbose) {
        console.log(colors.dim('  详细信息:'), details);
    }
}

// 检查嵌套字段是否存在
function hasNestedProperty(obj, path) {
    try {
        const keys = path.split(/[\.\[\]]+/).filter(key => key !== '');
        let current = obj;

        for (const key of keys) {
            if (key === '0' && Array.isArray(current) && current.length > 0) {
                current = current[0];
            } else if (current && typeof current === 'object' && key in current) {
                current = current[key];
            } else {
                return false;
            }
        }
        return true;
    } catch (e) {
        return false;
    }
}

// 测试单个API接口
function testApiEndpoint(serverKey, hostname, endpoint, apiConfig) {
    return new Promise((resolve) => {
        const startTime = Date.now();

        const options = {
            hostname: hostname,
            path: apiConfig.path,
            method: 'GET',
            timeout: config.timeout * 1000,
            headers: {
                'User-Agent': 'Comprehensive-Test/1.0',
                'Accept': 'application/json'
            }
        };

        log('debug', `请求 ${serverKey}: ${hostname}${apiConfig.path}`);

        const req = https.request(options, (res) => {
            let data = '';
            res.on('data', (chunk) => {
                data += chunk;
            });

            res.on('end', () => {
                const responseTime = Date.now() - startTime;
                let result = {
                    endpoint,
                    server: serverKey,
                    hostname,
                    success: false,
                    responseTime,
                    statusCode: res.statusCode,
                    error: null,
                    structureValid: false,
                    dataSize: data.length
                };

                try {
                    const json = JSON.parse(data);
                    result.success = res.statusCode === 200;

                    if (result.success) {
                        // 检查数据结构
                        if (apiConfig.expectedFields && Array.isArray(apiConfig.expectedFields)) {
                            const fieldCheck = apiConfig.expectedFields.every(field =>
                                hasNestedProperty(json, field)
                            );
                            result.structureValid = fieldCheck;

                            if (!fieldCheck) {
                                result.error = `数据结构不匹配，缺少字段: ${apiConfig.expectedFields.join(', ')}`;
                            }
                        } else {
                            result.structureValid = true;
                        }
                    } else {
                        result.error = `HTTP状态码错误: ${res.statusCode}`;
                    }

                } catch (e) {
                    result.error = `JSON解析失败: ${e.message}`;
                }

                log('debug', `${serverKey} 响应: ${result.success ? '成功' : '失败'} (${responseTime}ms)`);
                resolve(result);
            });
        });

        req.on('error', (e) => {
            const responseTime = Date.now() - startTime;
            log('debug', `${serverKey} 请求错误: ${e.message}`);
            resolve({
                endpoint,
                server: serverKey,
                hostname,
                success: false,
                responseTime,
                error: `请求失败: ${e.message}`,
                structureValid: false
            });
        });

        req.on('timeout', () => {
            req.destroy();
            const responseTime = Date.now() - startTime;
            log('debug', `${serverKey} 请求超时`);
            resolve({
                endpoint,
                server: serverKey,
                hostname,
                success: false,
                responseTime,
                error: '请求超时',
                structureValid: false
            });
        });

        req.end();
    });
}

// 重试机制
async function testWithRetry(serverKey, hostname, endpoint, apiConfig) {
    let lastError = null;

    for (let attempt = 1; attempt <= config.retry; attempt++) {
        try {
            const result = await testApiEndpoint(serverKey, hostname, endpoint, apiConfig);

            if (result.success && result.structureValid) {
                if (attempt > 1) {
                    log('info', `${apiConfig.description} 在第${attempt}次尝试后成功`);
                }
                return result;
            }

            lastError = result;

            if (attempt < config.retry) {
                log('warn', `${apiConfig.description} 第${attempt}次尝试失败，${config.retry - attempt}次重试剩余`);
                await new Promise(resolve => setTimeout(resolve, 1000 * attempt)); // 递增延迟
            }
        } catch (error) {
            lastError = { error: error.message };
        }
    }

    return lastError;
}

// API接口测试
async function runApiTests() {
    log('info', '🔍 开始API接口测试');

    const endpoints = Object.keys(API_ENDPOINTS);
    const servers = ENVIRONMENTS[config.env];

    testResults.summary.total = endpoints.length;

    for (const endpoint of endpoints) {
        const apiConfig = API_ENDPOINTS[endpoint];
        log('info', `测试 ${apiConfig.description} (${endpoint})`);

        // 测试主服务器
        const primaryResult = await testWithRetry('primary', servers.primary, endpoint, apiConfig);
        testResults.api_tests[`${endpoint}_primary`] = primaryResult;

        // 测试备用服务器
        const backupResult = await testWithRetry('backup', servers.backup, endpoint, apiConfig);
        testResults.api_tests[`${endpoint}_backup`] = backupResult;

        // 统计结果
        const primaryOk = primaryResult.success && primaryResult.structureValid;
        const backupOk = backupResult.success && backupResult.structureValid;

        if (primaryOk) testResults.summary.primary_available++;
        if (backupOk) testResults.summary.backup_available++;

        if (primaryOk || backupOk) {
            testResults.summary.passed++;
            log('success', `${apiConfig.description} - 正常`);
        } else {
            testResults.summary.failed++;
            if (apiConfig.critical) {
                testResults.summary.critical_failed++;
                log('error', `${apiConfig.description} - 关键API失败！`);
            } else {
                log('warn', `${apiConfig.description} - 非关键API失败`);
            }
        }
    }
}

// 服务器监控测试
async function runMonitorTests() {
    log('info', '📊 开始服务器监控测试');

    const servers = ENVIRONMENTS[config.env];
    const criticalApis = Object.keys(API_ENDPOINTS).filter(key => API_ENDPOINTS[key].critical);

    // 如果是纯monitor模式，需要设置总数并运行API测试
    if (config.mode === 'monitor') {
        testResults.summary.total = criticalApis.length;

        // 运行关键API测试并更新统计
        for (const endpoint of criticalApis) {
            const apiConfig = API_ENDPOINTS[endpoint];
            log('info', `测试 ${apiConfig.description} (${endpoint})`);

            // 测试主服务器
            const primaryResult = await testWithRetry('primary', servers.primary, endpoint, apiConfig);
            testResults.api_tests[`${endpoint}_primary`] = primaryResult;

            // 测试备用服务器
            const backupResult = await testWithRetry('backup', servers.backup, endpoint, apiConfig);
            testResults.api_tests[`${endpoint}_backup`] = backupResult;

            // 统计结果
            const primaryOk = primaryResult.success && primaryResult.structureValid;
            const backupOk = backupResult.success && backupResult.structureValid;

            if (primaryOk) testResults.summary.primary_available++;
            if (backupOk) testResults.summary.backup_available++;

            if (primaryOk || backupOk) {
                testResults.summary.passed++;
                log('success', `${apiConfig.description} - 正常`);
            } else {
                testResults.summary.failed++;
                if (apiConfig.critical) {
                    testResults.summary.critical_failed++;
                    log('error', `${apiConfig.description} - 关键API失败！`);
                } else {
                    log('warn', `${apiConfig.description} - 非关键API失败`);
                }
            }
        }
    }

    // 服务器健康状态分析
    for (const [serverKey, hostname] of Object.entries(servers)) {
        log('info', `监控服务器: ${serverKey} (${hostname})`);

        const serverResults = {
            hostname,
            total_tests: criticalApis.length,
            passed_tests: 0,
            failed_tests: 0,
            avg_response_time: 0,
            status: 'unknown'
        };

        let totalResponseTime = 0;

        // 统计该服务器的测试结果
        for (const endpoint of criticalApis) {
            const testKey = `${endpoint}_${serverKey}`;
            const result = testResults.api_tests[testKey];

            if (result) {
                if (result.success && result.structureValid) {
                    serverResults.passed_tests++;
                } else {
                    serverResults.failed_tests++;
                }
                totalResponseTime += result.responseTime || 0;
            }
        }

        serverResults.avg_response_time = criticalApis.length > 0 ? Math.round(totalResponseTime / criticalApis.length) : 0;

        // 判断服务器状态
        const successRate = serverResults.total_tests > 0 ? (serverResults.passed_tests / serverResults.total_tests) * 100 : 0;
        if (successRate >= 90) {
            serverResults.status = 'healthy';
            log('success', `${serverKey} 服务器状态: 健康 (${successRate.toFixed(1)}%)`);
        } else if (successRate >= 70) {
            serverResults.status = 'warning';
            log('warn', `${serverKey} 服务器状态: 警告 (${successRate.toFixed(1)}%)`);
        } else {
            serverResults.status = 'critical';
            log('error', `${serverKey} 服务器状态: 严重 (${successRate.toFixed(1)}%)`);
        }

        testResults.monitor_results[serverKey] = serverResults;
    }
}

// 登录功能测试
async function runLoginTests() {
    log('info', '🔐 开始登录功能测试');

    const loginApis = ['loginStatus', 'userAccount'];
    const servers = ENVIRONMENTS[config.env];

    for (const endpoint of loginApis) {
        if (!API_ENDPOINTS[endpoint]) continue;

        const apiConfig = API_ENDPOINTS[endpoint];
        log('info', `测试 ${apiConfig.description}`);

        for (const [serverKey, hostname] of Object.entries(servers)) {
            const result = await testApiEndpoint(serverKey, hostname, endpoint, apiConfig);

            // 分析登录状态
            let loginAnalysis = {
                server: serverKey,
                api_available: result.success,
                account_status: 'unknown',
                data_structure_valid: result.structureValid
            };

            if (result.success) {
                // 这里可以添加更详细的登录状态分析
                // 基于API返回的数据判断用户登录状态
                loginAnalysis.account_status = 'not_logged_in'; // 默认未登录
                log('info', `${serverKey} ${apiConfig.description}: API可用，用户未登录`);
            } else {
                log('warn', `${serverKey} ${apiConfig.description}: API不可用`);
            }

            testResults.login_tests[`${endpoint}_${serverKey}`] = loginAnalysis;
        }
    }
}

// 生成综合报告
function generateComprehensiveReport() {
    const { total, passed, failed, critical_failed, primary_available, backup_available } = testResults.summary;

    // 计算成功率
    testResults.summary.success_rate = total > 0 ? ((passed / total) * 100).toFixed(1) : '0.0';
    testResults.summary.primary_rate = total > 0 ? ((primary_available / total) * 100).toFixed(1) : '0.0';
    testResults.summary.backup_rate = total > 0 ? ((backup_available / total) * 100).toFixed(1) : '0.0';

    log('info', '=== 综合测试报告 ===');
    log('info', `测试模式: ${config.mode}`);
    log('info', `测试环境: ${config.env}`);
    log('info', `总接口数: ${total}`);
    log('info', `成功接口: ${passed} (${testResults.summary.success_rate}%)`);
    log('info', `失败接口: ${failed}`);
    log('info', `关键API失败: ${critical_failed}`);
    log('info', `主服务器可用率: ${testResults.summary.primary_rate}%`);
    log('info', `备用服务器可用率: ${testResults.summary.backup_rate}%`);

    // 保存详细报告
    const reportPath = path.join(__dirname, 'comprehensive_test_report.json');
    fs.writeFileSync(reportPath, JSON.stringify(testResults, null, 2));
    log('info', `详细报告已保存: ${reportPath}`);

    // 确定退出码
    let exitCode = 0;
    if (critical_failed > 0) {
        exitCode = 2; // 严重错误
        log('error', '检测到关键API失败，需要立即处理！');
    } else if (failed > 0) {
        exitCode = 1; // 警告
        log('warn', '检测到非关键API失败');
    } else {
        log('success', '所有测试通过');
    }

    return exitCode;
}

// 主函数
async function main() {
    try {
        log('info', colors.bold('🚀 综合测试开始'));
        log('info', `配置: 模式=${config.mode}, 环境=${config.env}, 超时=${config.timeout}s, 重试=${config.retry}次`);

        if (config.mode === 'api' || config.mode === 'all') {
            await runApiTests();
        }

        if (config.mode === 'monitor' || config.mode === 'all') {
            await runMonitorTests();
        }

        if (config.mode === 'login' || config.mode === 'all') {
            await runLoginTests();
        }

        const exitCode = generateComprehensiveReport();
        log('success', '✨ 综合测试完成');

        process.exit(exitCode);
    } catch (error) {
        log('error', `测试过程中发生错误: ${error.message}`);
        process.exit(2);
    }
}

// 启动测试
if (require.main === module) {
    main();
}

module.exports = {
    runApiTests,
    runMonitorTests,
    runLoginTests,
    generateComprehensiveReport,
    testResults,
    SERVERS,
    API_ENDPOINTS
};
