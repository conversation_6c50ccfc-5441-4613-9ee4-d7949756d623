package com.example.aimusicplayer.data.model

import android.os.Parcelable
import kotlinx.parcelize.Parcelize
import kotlinx.parcelize.RawValue

/**
 * 歌曲模型
 */
@Parcelize
data class Song(
    val id: Long,
    val name: String,
    val ar: @RawValue List<Artist>? = null,
    val al: @RawValue Album? = null,
    val dt: Long = 0, // 时长，单位毫秒
    val publishTime: Long = 0,
    val picUrl: String? = null, // 兼容新歌速递接口
    val cover: String? = null, // 歌曲封面URL（备用字段）
    val song: @RawValue SongInfo? = null, // 兼容新歌速递接口
    val fee: Int = 0 // 收费类型：0=免费，1=VIP歌曲，4=付费歌曲
) : Parcelable {
    // 兼容新歌速递接口
    fun getActualArtists(): List<Artist> {
        return ar ?: song?.artists ?: emptyList()
    }

    // 兼容新歌速递接口
    fun getActualAlbum(): Album {
        return al ?: song?.album ?: Album()
    }

    // 获取歌手名称
    fun getArtistNames(): String {
        return getActualArtists().joinToString(", ") { it.name }
    }

    // 获取专辑封面URL（旧方法，保持兼容性）
    fun getAlbumCoverUrl(): String? {
        // 优先使用专辑的picUrl
        val albumPicUrl = getActualAlbum().getImageUrl()
        if (albumPicUrl.isNotEmpty()) {
            return albumPicUrl
        }

        // 如果专辑封面为空，使用歌曲的picUrl（兼容新歌速递接口）
        return picUrl
    }

    // 获取歌曲封面URL（新方法，严格优先级）
    fun getSongCoverUrl(): String? {
        // 第一优先级：picUrl
        if (!picUrl.isNullOrEmpty()) {
            return picUrl
        }

        // 第二优先级：cover
        if (!cover.isNullOrEmpty()) {
            return cover
        }

        return null
    }

    // 获取专辑封面URL（新方法，严格优先级）
    fun getAlbumOnlyCoverUrl(): String? {
        return getActualAlbum().getImageUrl().takeIf { it.isNotEmpty() }
    }


}

/**
 * 歌曲信息模型（兼容新歌速递接口）
 */
@Parcelize
data class SongInfo(
    val id: Long,
    val name: String,
    val artists: @RawValue List<Artist>,
    val album: @RawValue Album
) : Parcelable
