package com.example.aimusicplayer.viewmodel

import android.app.Application
import android.util.Log // Added import
import androidx.lifecycle.LiveData
import androidx.lifecycle.viewModelScope
import com.example.aimusicplayer.data.model.Song
import com.example.aimusicplayer.data.repository.MusicRepository
import com.example.aimusicplayer.data.repository.UserRepository
import com.example.aimusicplayer.error.GlobalErrorHandler
import com.example.aimusicplayer.utils.NetworkResult
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * 示例ViewModel
 * 展示如何使用FlowViewModel基类
 */
@HiltViewModel
class ExampleViewModel @Inject constructor(
    application: Application,
    private val musicRepository: MusicRepository,
    private val userRepository: UserRepository,
    errorHandler: GlobalErrorHandler
) : FlowViewModel(application) {

    companion object {
        private const val TAG = "ExampleViewModel" // Added TAG
    }

    init {
        // 设置错误处理器
        this.errorHandler = errorHandler
    }

    // 使用StateFlow管理UI状态
    private val _songs = MutableStateFlow<List<Song>>(emptyList())
    val songs: StateFlow<List<Song>> = _songs.toStateFlow()

    // 为了兼容旧代码，提供LiveData版本
    val songsLiveData: LiveData<List<Song>> = songs.toLiveData()

    // 加载状态 - 使用基类提供的状态
    val isLoading: StateFlow<Boolean> = loadingFlow
    val isLoadingLiveData: LiveData<Boolean> = loading

    // 错误消息 - 使用基类提供的状态
    val error: StateFlow<String?> = errorMessageFlow.toStateFlow(null)
    val errorLiveData: LiveData<String> = errorMessage

    // 是否登录
    val isLoggedIn: StateFlow<Boolean> = userRepository.loginStatusFlow
    val isLoggedInLiveData: LiveData<Boolean> = userRepository.loginStatus

    init {
        // 加载数据
        loadNewSongs()
    }

    /**
     * 加载新歌速递
     * 使用Flow API
     */
    fun loadNewSongs() {
        // 使用基类提供的launchSafely方法
        launchSafely {
            // 使用Repository的Flow API
            musicRepository.getNewSongsFlow().collectLatest { result ->
                when (val networkResult = result) {
                    is NetworkResult.Success -> {
                        _songs.value = networkResult.data
                    }
                    is NetworkResult.Error -> {
                        handleError(Exception(networkResult.message))
                    }
                    is NetworkResult.Loading -> {
                        setLoading(true)
                    }
                }
            }
        }
    }

    /**
     * 加载新歌速递
     * 使用协程API
     */
    fun loadNewSongsWithCoroutine() {
        // 使用基类提供的launch方法
        launch {
            setLoading(true)
            try {
                val songs = musicRepository.getNewSongs()
                _songs.value = songs
                setLoading(false)
            } catch (e: Exception) {
                handleError(e)
                setLoading(false)
            }
        }
    }

    /**
     * 使用apiFlow辅助方法
     */
    fun loadNewSongsWithApiFlow() {
        viewModelScope.launch {
            apiFlow(call = { musicRepository.getNewSongs() }).collectLatest { result ->
                when (val networkResult = result) {
                    is NetworkResult.Success -> {
                        _songs.value = networkResult.data
                    }
                    is NetworkResult.Error -> {
                        // 错误已由apiFlow处理
                    }
                    is NetworkResult.Loading -> {
                        // 加载状态已由apiFlow处理
                    }
                }
            }
        }
    }

    /**
     * 退出登录 - 简化版本
     */
    fun logout() {
        viewModelScope.launch {
            try {
                setLoading(true)
                userRepository.logout()
                // 退出登录成功，UserRepository已经清除了登录状态
            } catch (e: Exception) {
                // 即使API调用失败，UserRepository也会清除登录状态
                handleError(e)
            } finally {
                setLoading(false)
            }
        }
    }
}
