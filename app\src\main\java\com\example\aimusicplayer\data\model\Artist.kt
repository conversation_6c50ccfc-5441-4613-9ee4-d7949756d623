package com.example.aimusicplayer.data.model

import com.google.gson.annotations.SerializedName

/**
 * 艺术家模型类
 * 用于统一项目中的艺术家数据结构
 */
data class Artist(
    var id: Long = 0,
    var name: String = "",

    @SerializedName("tns")
    var translations: List<String>? = null,

    @SerializedName("alias")
    var aliases: List<String>? = null
) {
    /**
     * 获取艺术家显示名称
     * 如果名称为空，返回"未知艺术家"
     */
    fun getDisplayName(): String {
        return name.ifEmpty { "未知艺术家" }
    }
}
