package com.example.aimusicplayer.data.model

import com.google.gson.annotations.SerializedName

/**
 * 登录状态响应
 * 对应/login/status接口返回的数据结构
 * 
 * 根据API测试结果，实际返回结构为：
 * {
 *   "data": {
 *     "code": 200,
 *     "account": null,
 *     "profile": null
 *   }
 * }
 */
data class LoginStatusResponse(
    @SerializedName("data")
    val data: LoginStatusData
) {
    /**
     * 登录状态数据
     */
    data class LoginStatusData(
        @SerializedName("code")
        val code: Int = 0,
        
        @SerializedName("account")
        val account: Account? = null,
        
        @SerializedName("profile")
        val profile: Profile? = null
    ) {
        /**
         * 账户信息
         */
        data class Account(
            @SerializedName("id")
            val id: Long = 0,
            
            @SerializedName("userName")
            val userName: String = "",
            
            @SerializedName("type")
            val type: Int = 0,
            
            @SerializedName("status")
            val status: Int = 0,
            
            @SerializedName("whitelistAuthority")
            val whitelistAuthority: Int = 0,
            
            @SerializedName("createTime")
            val createTime: Long = 0,
            
            @SerializedName("tokenVersion")
            val tokenVersion: Int = 0,
            
            @SerializedName("ban")
            val ban: Int = 0,
            
            @SerializedName("baoyueVersion")
            val baoyueVersion: Int = 0,
            
            @SerializedName("donateVersion")
            val donateVersion: Int = 0,
            
            @SerializedName("vipType")
            val vipType: Int = 0,
            
            @SerializedName("anonimousUser")
            val anonimousUser: Boolean = false,
            
            @SerializedName("paidFee")
            val paidFee: Boolean = false
        )
        
        /**
         * 用户资料
         */
        data class Profile(
            @SerializedName("userId")
            val userId: Long = 0,
            
            @SerializedName("userType")
            val userType: Int = 0,
            
            @SerializedName("nickname")
            val nickname: String = "",
            
            @SerializedName("avatarImgId")
            val avatarImgId: Long = 0,
            
            @SerializedName("avatarUrl")
            val avatarUrl: String = "",
            
            @SerializedName("backgroundImgId")
            val backgroundImgId: Long = 0,
            
            @SerializedName("backgroundUrl")
            val backgroundUrl: String = "",
            
            @SerializedName("signature")
            val signature: String? = null,
            
            @SerializedName("createTime")
            val createTime: Long = 0,
            
            @SerializedName("userName")
            val userName: String = "",
            
            @SerializedName("accountType")
            val accountType: Int = 0,
            
            @SerializedName("shortUserName")
            val shortUserName: String = "",
            
            @SerializedName("birthday")
            val birthday: Long = 0,
            
            @SerializedName("authority")
            val authority: Int = 0,
            
            @SerializedName("gender")
            val gender: Int = 0,
            
            @SerializedName("accountStatus")
            val accountStatus: Int = 0,
            
            @SerializedName("province")
            val province: Int = 0,
            
            @SerializedName("city")
            val city: Int = 0,
            
            @SerializedName("authStatus")
            val authStatus: Int = 0,
            
            @SerializedName("description")
            val description: String? = null,
            
            @SerializedName("detailDescription")
            val detailDescription: String? = null,
            
            @SerializedName("defaultAvatar")
            val defaultAvatar: Boolean = false,
            
            @SerializedName("expertTags")
            val expertTags: List<String>? = null,
            
            @SerializedName("experts")
            val experts: Map<String, String>? = null,
            
            @SerializedName("djStatus")
            val djStatus: Int = 0,
            
            @SerializedName("locationStatus")
            val locationStatus: Int = 0,
            
            @SerializedName("vipType")
            val vipType: Int = 0,
            
            @SerializedName("followed")
            val followed: Boolean = false,
            
            @SerializedName("mutual")
            val mutual: Boolean = false,
            
            @SerializedName("authenticated")
            val authenticated: Boolean = false,
            
            @SerializedName("lastLoginTime")
            val lastLoginTime: Long = 0,
            
            @SerializedName("lastLoginIP")
            val lastLoginIP: String = "",
            
            @SerializedName("remarkName")
            val remarkName: String? = null,
            
            @SerializedName("viptypeVersion")
            val viptypeVersion: Long = 0,
            
            @SerializedName("authenticationTypes")
            val authenticationTypes: Int = 0,
            
            @SerializedName("avatarDetail")
            val avatarDetail: String? = null,
            
            @SerializedName("anchor")
            val anchor: Boolean = false
        )
    }
    
    /**
     * 检查是否已登录
     * @return 如果已登录返回true，否则返回false
     */
    fun isLoggedIn(): Boolean {
        return data.code == 200 && data.account != null && data.profile != null
    }
    
    /**
     * 获取用户ID
     * @return 用户ID，如果未登录返回null
     */
    fun getUserId(): String? {
        return if (isLoggedIn()) {
            data.profile?.userId?.toString()
        } else {
            null
        }
    }
    
    /**
     * 获取用户昵称
     * @return 用户昵称，如果未登录返回null
     */
    fun getNickname(): String? {
        return if (isLoggedIn()) {
            data.profile?.nickname
        } else {
            null
        }
    }
    
    /**
     * 获取用户头像URL
     * @return 用户头像URL，如果未登录返回null
     */
    fun getAvatarUrl(): String? {
        return if (isLoggedIn()) {
            data.profile?.avatarUrl
        } else {
            null
        }
    }
}
