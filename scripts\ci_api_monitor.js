#!/usr/bin/env node

/**
 * CI/CD API监控脚本
 * 自动监控API状态，适用于持续集成流程
 *
 * 使用方法：
 * node scripts/ci_api_monitor.js [--env=prod|dev] [--timeout=30] [--retry=3]
 *
 * 退出码：
 * 0 - 所有API正常
 * 1 - 部分API异常但在可接受范围内
 * 2 - 严重API异常，需要立即处理
 */

const https = require('https');
const fs = require('fs');
const path = require('path');

// 命令行参数解析
const args = process.argv.slice(2);
const config = {
    env: 'prod',
    timeout: 30,
    retry: 3,
    verbose: false
};

args.forEach(arg => {
    if (arg.startsWith('--env=')) {
        config.env = arg.split('=')[1];
    } else if (arg.startsWith('--timeout=')) {
        config.timeout = parseInt(arg.split('=')[1]);
    } else if (arg.startsWith('--retry=')) {
        config.retry = parseInt(arg.split('=')[1]);
    } else if (arg === '--verbose') {
        config.verbose = true;
    }
});

// 环境配置
const ENVIRONMENTS = {
    prod: {
        primary: '1355831898-4499wupl9z.ap-guangzhou.tencentscf.com',
        backup: 'zm.armoe.cn'
    },
    dev: {
        primary: '1355831898-4499wupl9z.ap-guangzhou.tencentscf.com',
        backup: 'zm.armoe.cn'
    }
};

// 关键API接口配置（仅监控核心功能）
const CRITICAL_APIS = {
    cloudsearch: {
        path: '/cloudsearch?keywords=%E5%91%A8%E6%9D%B0%E4%BC%A6&type=1&limit=1',
        expectedFields: ['code', 'result'],
        description: '搜索功能',
        critical: true
    },
    songDetail: {
        path: '/song/detail?ids=347230',
        expectedFields: ['code', 'songs'],
        description: '歌曲详情',
        critical: true
    },
    lyric: {
        path: '/lyric?id=347230',
        expectedFields: ['code', 'lrc'],
        description: '歌词功能',
        critical: false
    },
    loginStatus: {
        path: '/login/status',
        expectedFields: ['data', 'data.code'],
        description: '登录状态',
        critical: false
    }
};

// 监控结果
let monitorResults = {
    timestamp: new Date().toISOString(),
    environment: config.env,
    servers: ENVIRONMENTS[config.env],
    results: {},
    summary: {
        total: 0,
        passed: 0,
        failed: 0,
        critical_failed: 0,
        primary_available: 0,
        backup_available: 0
    }
};

// 颜色输出（CI环境兼容）
const colors = {
    green: (text) => process.env.CI ? text : `\x1b[32m${text}\x1b[0m`,
    red: (text) => process.env.CI ? text : `\x1b[31m${text}\x1b[0m`,
    yellow: (text) => process.env.CI ? text : `\x1b[33m${text}\x1b[0m`,
    blue: (text) => process.env.CI ? text : `\x1b[34m${text}\x1b[0m`,
    bold: (text) => process.env.CI ? text : `\x1b[1m${text}\x1b[0m`
};

// 日志函数
function log(level, message) {
    const timestamp = new Date().toISOString();
    const prefix = `[${timestamp}] [${level.toUpperCase()}]`;

    switch (level) {
        case 'info':
            console.log(colors.blue(prefix), message);
            break;
        case 'warn':
            console.warn(colors.yellow(prefix), message);
            break;
        case 'error':
            console.error(colors.red(prefix), message);
            break;
        case 'success':
            console.log(colors.green(prefix), message);
            break;
        default:
            console.log(prefix, message);
    }
}

// 检查字段是否存在
function hasNestedProperty(obj, path) {
    try {
        const keys = path.split(/[\.\[\]]+/).filter(key => key !== '');
        let current = obj;

        for (const key of keys) {
            if (key === '0' && Array.isArray(current) && current.length > 0) {
                current = current[0];
            } else if (current && typeof current === 'object' && key in current) {
                current = current[key];
            } else {
                return false;
            }
        }
        return true;
    } catch (e) {
        return false;
    }
}

// 测试单个API接口
function testApiEndpoint(serverKey, hostname, endpoint, apiConfig) {
    return new Promise((resolve) => {
        const startTime = Date.now();

        const options = {
            hostname: hostname,
            path: apiConfig.path,
            method: 'GET',
            timeout: config.timeout * 1000,
            headers: {
                'User-Agent': 'CI-API-Monitor/1.0',
                'Accept': 'application/json'
            }
        };

        const req = https.request(options, (res) => {
            let data = '';
            res.on('data', (chunk) => {
                data += chunk;
            });

            res.on('end', () => {
                const responseTime = Date.now() - startTime;
                let result = {
                    endpoint,
                    server: serverKey,
                    hostname,
                    success: false,
                    responseTime,
                    statusCode: res.statusCode,
                    error: null,
                    structureValid: false
                };

                try {
                    const json = JSON.parse(data);
                    result.success = res.statusCode === 200;

                    if (result.success) {
                        // 检查数据结构
                        if (config.expectedFields && Array.isArray(config.expectedFields)) {
                            const fieldCheck = config.expectedFields.every(field =>
                                hasNestedProperty(json, field)
                            );
                            result.structureValid = fieldCheck;
                        } else {
                            result.structureValid = true; // 如果没有定义expectedFields，认为结构有效
                        }
                    }

                } catch (e) {
                    result.error = `JSON解析失败: ${e.message}`;
                }

                resolve(result);
            });
        });

        req.on('error', (e) => {
            resolve({
                endpoint,
                server: serverKey,
                hostname,
                success: false,
                responseTime: Date.now() - startTime,
                error: `请求失败: ${e.message}`,
                structureValid: false
            });
        });

        req.on('timeout', () => {
            req.destroy();
            resolve({
                endpoint,
                server: serverKey,
                hostname,
                success: false,
                responseTime: Date.now() - startTime,
                error: '请求超时',
                structureValid: false
            });
        });

        req.end();
    });
}

// 重试机制
async function testWithRetry(serverKey, hostname, endpoint, apiConfig) {
    let lastError = null;

    for (let attempt = 1; attempt <= config.retry; attempt++) {
        try {
            // 传递timeout配置
            const configWithTimeout = { ...apiConfig, timeout: config.timeout };
            const result = await testApiEndpoint(serverKey, hostname, endpoint, configWithTimeout);

            if (result.success && result.structureValid) {
                if (attempt > 1) {
                    log('info', `${apiConfig.description} 在第${attempt}次尝试后成功`);
                }
                return result;
            }

            lastError = result;

            if (attempt < config.retry) {
                log('warn', `${apiConfig.description} 第${attempt}次尝试失败，${config.retry - attempt}次重试剩余`);
                await new Promise(resolve => setTimeout(resolve, 1000 * attempt)); // 递增延迟
            }
        } catch (error) {
            lastError = { error: error.message };
        }
    }

    return lastError;
}

// 执行监控
async function runMonitoring() {
    log('info', `开始API监控 - 环境: ${config.env}`);
    log('info', `配置: 超时=${config.timeout}s, 重试=${config.retry}次`);

    const servers = ENVIRONMENTS[config.env];
    const endpoints = Object.keys(CRITICAL_APIS);

    monitorResults.summary.total = endpoints.length;

    for (const endpoint of endpoints) {
        const apiConfig = CRITICAL_APIS[endpoint];
        log('info', `测试 ${apiConfig.description} (${endpoint})`);

        // 测试主服务器
        const primaryResult = await testWithRetry('primary', servers.primary, endpoint, apiConfig);
        monitorResults.results[`${endpoint}_primary`] = primaryResult;

        // 测试备用服务器
        const backupResult = await testWithRetry('backup', servers.backup, endpoint, apiConfig);
        monitorResults.results[`${endpoint}_backup`] = backupResult;

        // 统计结果
        const primaryOk = primaryResult.success && primaryResult.structureValid;
        const backupOk = backupResult.success && backupResult.structureValid;

        if (primaryOk) monitorResults.summary.primary_available++;
        if (backupOk) monitorResults.summary.backup_available++;

        if (primaryOk || backupOk) {
            monitorResults.summary.passed++;
            log('success', `${apiConfig.description} - 正常`);
        } else {
            monitorResults.summary.failed++;
            if (apiConfig.critical) {
                monitorResults.summary.critical_failed++;
                log('error', `${apiConfig.description} - 关键API失败！`);
            } else {
                log('warn', `${apiConfig.description} - 非关键API失败`);
            }
        }
    }
}

// 生成报告
function generateReport() {
    const { total, passed, failed, critical_failed, primary_available, backup_available } = monitorResults.summary;
    const successRate = ((passed / total) * 100).toFixed(1);
    const primaryRate = ((primary_available / total) * 100).toFixed(1);
    const backupRate = ((backup_available / total) * 100).toFixed(1);

    log('info', '=== API监控报告 ===');
    log('info', `总接口数: ${total}`);
    log('info', `成功接口: ${passed} (${successRate}%)`);
    log('info', `失败接口: ${failed}`);
    log('info', `关键API失败: ${critical_failed}`);
    log('info', `主服务器可用率: ${primaryRate}%`);
    log('info', `备用服务器可用率: ${backupRate}%`);

    // 保存详细报告
    const reportPath = path.join(__dirname, '..', 'ci_api_report.json');
    fs.writeFileSync(reportPath, JSON.stringify(monitorResults, null, 2));
    log('info', `详细报告已保存: ${reportPath}`);

    // 确定退出码
    let exitCode = 0;
    if (critical_failed > 0) {
        exitCode = 2; // 严重错误
        log('error', '检测到关键API失败，需要立即处理！');
    } else if (failed > 0) {
        exitCode = 1; // 警告
        log('warn', '检测到非关键API失败');
    } else {
        log('success', '所有API运行正常');
    }

    return exitCode;
}

// 主函数
async function main() {
    try {
        await runMonitoring();
        const exitCode = generateReport();
        process.exit(exitCode);
    } catch (error) {
        log('error', `监控过程中发生错误: ${error.message}`);
        process.exit(2);
    }
}

// 启动监控
if (require.main === module) {
    main();
}

module.exports = { runMonitoring, generateReport };
