package com.example.aimusicplayer.data.source

import com.example.aimusicplayer.data.model.BaseResponse
import com.example.aimusicplayer.data.model.CommentResponse
import com.example.aimusicplayer.data.model.LoginStatusResponse
import com.example.aimusicplayer.data.model.LyricResponse
import com.example.aimusicplayer.data.model.NewSongsResponse
import com.example.aimusicplayer.data.model.SearchResponse
import com.example.aimusicplayer.data.model.SearchSuggestResponse
import com.example.aimusicplayer.data.model.SongDetailResponse
import com.example.aimusicplayer.data.model.UserDetailResponse
import com.example.aimusicplayer.data.model.UserSubCountResponse
import okhttp3.ResponseBody
import retrofit2.http.GET
import retrofit2.http.POST
import retrofit2.http.Query
import retrofit2.http.FormUrlEncoded
import retrofit2.http.Field

/**
 * API服务接口
 * 定义了与服务器交互的各种API
 */
interface ApiService {
    /**
     * 获取歌曲详情
     */
    @GET("/song/detail")
    suspend fun getSongDetail(@Query("ids") id: Long): com.example.aimusicplayer.data.model.SongDetailResponse

    /**
     * 获取新歌速递 - 修正为正确的API接口
     * @param type 地区类型 id，全部:0, 华语:7, 欧美:96, 日本:8, 韩国:16
     */
    @GET("/top/song")
    suspend fun getNewSongs(@Query("type") type: Int = 0): NewSongsResponse

    /**
     * 获取歌曲详情 - 包含完整的专辑封面信息
     * @param ids 歌曲ID，支持多个ID用逗号分隔
     */
    @GET("/song/detail")
    suspend fun getSongDetail(@Query("ids") ids: String): SongDetailResponse

    /**
     * 获取歌词
     */
    @GET("/lyric")
    suspend fun getLyric(@Query("id") id: Long): LyricResponse

    /**
     * 获取歌曲URL
     */
    @GET("/song/url")
    suspend fun getSongUrl(@Query("id") id: Long): BaseResponse

    /**
     * 获取Banner
     */
    @GET("/banner")
    suspend fun getBanners(): BaseResponse

    /**
     * 获取推荐歌单
     */
    @GET("/personalized")
    suspend fun getRecommendPlaylists(@Query("limit") limit: Int = 30): BaseResponse

    /**
     * 获取所有榜单
     */
    @GET("/toplist")
    suspend fun getToplists(): BaseResponse

    /**
     * 获取新碟上架
     */
    @GET("/album/newest")
    suspend fun getNewAlbums(): BaseResponse

    /**
     * 获取推荐歌曲
     */
    @GET("/recommend/songs")
    suspend fun getRecommendedSongs(): SongDetailResponse

    /**
     * 获取歌单详情
     */
    @GET("/playlist/detail")
    suspend fun getPlaylistDetail(@Query("id") id: Long): BaseResponse

    /**
     * 获取歌单歌曲
     */
    @GET("/playlist/track/all")
    suspend fun getPlaylistTracks(
        @Query("id") id: Long,
        @Query("limit") limit: Int = 1000,
        @Query("offset") offset: Int = 0
    ): BaseResponse

    /**
     * 搜索
     */
    @GET("/search")
    suspend fun search(
        @Query("keywords") keywords: String,
        @Query("type") type: Int = 1,
        @Query("limit") limit: Int = 30,
        @Query("offset") offset: Int = 0
    ): BaseResponse

    /**
     * 云搜索（更全面的搜索）
     */
    @GET("/cloudsearch")
    suspend fun cloudSearch(
        @Query("keywords") keywords: String,
        @Query("type") type: Int = 1,
        @Query("limit") limit: Int = 30,
        @Query("offset") offset: Int = 0
    ): SearchResponse

    /**
     * 搜索建议
     */
    @GET("/search/suggest")
    suspend fun searchSuggest(
        @Query("keywords") keywords: String,
        @Query("type") type: String = "mobile"
    ): SearchSuggestResponse

    /**
     * 获取二维码Key
     */
    @GET("/login/qr/key")
    suspend fun getQrKey(@Query("timestamp") timestamp: Long = System.currentTimeMillis()): ResponseBody

    /**
     * 获取二维码图片
     * @param key 二维码Key
     * @param qrimg 是否返回二维码图片base64
     */
    @GET("/login/qr/create")
    suspend fun getQrImage(
        @Query("key") key: String,
        @Query("qrimg") qrimg: Boolean = true,
        @Query("timestamp") timestamp: Long = System.currentTimeMillis()
    ): ResponseBody

    /**
     * 检查二维码状态
     * @param key 二维码Key
     * @param noCookie 不携带cookie
     */
    @GET("/login/qr/check")
    suspend fun checkQrStatus(
        @Query("key") key: String,
        @Query("noCookie") noCookie: Boolean = true,
        @Query("timestamp") timestamp: Long = System.currentTimeMillis()
    ): ResponseBody

    /**
     * 游客登录
     * @param timestamp 时间戳
     */
    @POST("/register/anonimous")
    suspend fun guestLogin(@Query("timestamp") timestamp: Long = System.currentTimeMillis()): ResponseBody



    /**
     * 检查登录状态
     */
    @POST("/login/status")
    suspend fun checkLoginStatus(@Query("timestamp") timestamp: Long = System.currentTimeMillis()): ResponseBody

    /**
     * 检查登录状态 - 返回结构化数据
     */
    @POST("/login/status")
    suspend fun getLoginStatus(@Query("timestamp") timestamp: Long = System.currentTimeMillis()): LoginStatusResponse

    /**
     * 获取用户账号信息
     */
    @GET("/user/account")
    suspend fun getUserAccount(@Query("timestamp") timestamp: Long = System.currentTimeMillis()): ResponseBody

    /**
     * 发送验证码
     * @param phone 手机号
     * @param ctcode 国家区号，默认86
     */
    @FormUrlEncoded
    @POST("/captcha/sent")
    suspend fun sendCaptcha(
        @Field("phone") phone: String,
        @Field("ctcode") ctcode: String = "86",
        @Field("timestamp") timestamp: Long = System.currentTimeMillis()
    ): ResponseBody

    /**
     * 验证验证码
     * @param phone 手机号
     * @param captcha 验证码
     * @param ctcode 国家区号，默认86
     */
    @FormUrlEncoded
    @POST("/captcha/verify")
    suspend fun verifyCaptcha(
        @Field("phone") phone: String,
        @Field("captcha") captcha: String,
        @Field("ctcode") ctcode: String = "86",
        @Field("timestamp") timestamp: Long = System.currentTimeMillis()
    ): ResponseBody

    /**
     * 使用验证码登录
     * @param phone 手机号
     * @param captcha 验证码
     * @param countrycode 国家区号，默认86
     */
    @FormUrlEncoded
    @POST("/login/cellphone")
    suspend fun loginWithCaptcha(
        @Field("phone") phone: String,
        @Field("captcha") captcha: String,
        @Field("countrycode") countrycode: String = "86",
        @Field("timestamp") timestamp: Long = System.currentTimeMillis()
    ): ResponseBody

    /**
     * 使用手机号密码登录
     * @param phone 手机号
     * @param password 密码
     * @param countrycode 国家区号，默认86
     */
    @FormUrlEncoded
    @POST("/login/cellphone")
    suspend fun loginWithPhone(
        @Field("phone") phone: String,
        @Field("password") password: String,
        @Field("countrycode") countrycode: String = "86",
        @Field("timestamp") timestamp: Long = System.currentTimeMillis()
    ): ResponseBody

    /**
     * 退出登录
     */
    @POST("/logout")
    suspend fun logout(@Query("timestamp") timestamp: Long = System.currentTimeMillis()): ResponseBody

    /**
     * 获取用户详情
     * @param uid 用户ID
     */
    @GET("/user/detail")
    suspend fun getUserDetail(@Query("uid") uid: String): UserDetailResponse

    /**
     * 获取用户信息，歌单，收藏，mv, dj数量
     */
    @GET("/user/subcount")
    suspend fun getUserSubCount(): UserSubCountResponse

    /**
     * 获取相似歌曲
     * @param id 歌曲ID
     */
    @GET("/simi/song")
    suspend fun getSimiSongs(@Query("id") id: Long): SongDetailResponse

    /**
     * 获取心动模式/智能播放列表
     * @param id 歌曲ID
     * @param pid 歌单ID
     */
    @GET("/playmode/intelligence/list")
    suspend fun getIntelligenceList(
        @Query("id") id: Long,
        @Query("pid") pid: Long
    ): SongDetailResponse

    /**
     * 获取歌曲评论
     * @param id 歌曲ID
     * @param limit 评论数量
     * @param offset 偏移量
     */
    @GET("/comment/music")
    suspend fun getSongComments(
        @Query("id") id: Long,
        @Query("limit") limit: Int = 50,
        @Query("offset") offset: Int = 0
    ): CommentResponse

    /**
     * 获取专辑评论
     * @param id 专辑ID
     * @param limit 评论数量
     * @param offset 偏移量
     */
    @GET("/comment/album")
    suspend fun getAlbumComments(
        @Query("id") id: Long,
        @Query("limit") limit: Int = 50,
        @Query("offset") offset: Int = 0
    ): CommentResponse

    /**
     * 获取歌单评论
     * @param id 歌单ID
     * @param limit 评论数量
     * @param offset 偏移量
     */
    @GET("/comment/playlist")
    suspend fun getPlaylistComments(
        @Query("id") id: Long,
        @Query("limit") limit: Int = 50,
        @Query("offset") offset: Int = 0
    ): CommentResponse

    /**
     * 兼容旧版本的评论接口
     */
    @GET("/comment/music")
    suspend fun getComments(
        @Query("id") id: Long,
        @Query("limit") limit: Int = 50,
        @Query("offset") offset: Int = 0
    ): CommentResponse

    /**
     * 发送评论
     * @param id 资源ID
     * @param content 评论内容
     * @param type 评论类型，0: 歌曲，1: mv，2: 歌单，3: 专辑，4: 电台，5: 视频，6: 动态
     */
    @POST("/comment")
    suspend fun sendComment(
        @Query("id") id: Long,
        @Query("content") content: String,
        @Query("type") type: Int = 0
    ): BaseResponse

    /**
     * 回复评论
     * @param id 资源ID
     * @param commentId 评论ID
     * @param content 评论内容
     * @param type 评论类型，0: 歌曲，1: mv，2: 歌单，3: 专辑，4: 电台，5: 视频，6: 动态
     */
    @POST("/comment/reply")
    suspend fun replyComment(
        @Query("id") id: Long,
        @Query("commentId") commentId: Long,
        @Query("content") content: String,
        @Query("type") type: Int = 0
    ): BaseResponse

    /**
     * 点赞评论
     * @param id 资源ID
     * @param cid 评论ID
     * @param t 是否点赞，1: 点赞，0: 取消点赞
     * @param type 资源类型，0: 歌曲，1: mv，2: 歌单，3: 专辑，4: 电台，5: 视频，6: 动态
     */
    @POST("/comment/like")
    suspend fun likeComment(
        @Query("id") id: Long,
        @Query("cid") cid: Long,
        @Query("t") t: Int = 1,
        @Query("type") type: Int = 0
    ): BaseResponse

    /**
     * 获取热门评论
     * @param id 歌曲ID
     * @param limit 评论数量
     * @param type 资源类型，0: 歌曲，1: mv，2: 歌单，3: 专辑，4: 电台，5: 视频，6: 动态
     */
    @GET("/comment/hot")
    suspend fun getHotComments(
        @Query("id") id: Long,
        @Query("limit") limit: Int = 20,
        @Query("type") type: Int = 0
    ): CommentResponse

    /**
     * 检查歌曲收藏状态
     * @param id 歌曲ID
     */
    @GET("/like/status")
    suspend fun checkLikeStatus(@Query("id") id: Long): BaseResponse

    /**
     * 收藏歌曲
     * @param id 歌曲ID
     * @param like 是否收藏，true: 收藏，false: 取消收藏
     */
    @POST("/like")
    suspend fun likeSong(
        @Query("id") id: Long,
        @Query("like") like: Boolean = true
    ): BaseResponse

    /**
     * 取消收藏歌曲
     * @param id 歌曲ID
     */
    @POST("/like")
    suspend fun unlikeSong(
        @Query("id") id: Long,
        @Query("like") like: Boolean = false
    ): BaseResponse

    /**
     * 获取用户歌单
     * @param uid 用户ID
     * @param limit 返回数量限制，默认30
     * @param offset 偏移数量，用于分页，默认0
     */
    @GET("/user/playlist")
    suspend fun getUserPlaylists(
        @Query("uid") uid: Long,
        @Query("limit") limit: Int = 30,
        @Query("offset") offset: Int = 0
    ): BaseResponse

    /**
     * 获取当前登录用户的歌单
     * @param limit 返回数量限制，默认30
     * @param offset 偏移数量，用于分页，默认0
     */
    @GET("/user/playlist")
    suspend fun getUserPlaylists(
        @Query("limit") limit: Int = 30,
        @Query("offset") offset: Int = 0
    ): BaseResponse

    /**
     * 收藏/取消收藏歌单
     * @param t 类型，1:收藏，2:取消收藏
     * @param id 歌单ID
     */
    @POST("/playlist/subscribe")
    suspend fun subscribePlaylist(
        @Query("t") t: Int,
        @Query("id") id: Long
    ): BaseResponse
}
