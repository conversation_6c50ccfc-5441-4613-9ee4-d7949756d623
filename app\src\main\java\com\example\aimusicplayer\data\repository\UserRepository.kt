package com.example.aimusicplayer.data.repository

import android.content.Context
import android.content.SharedPreferences
import android.util.Log
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.asLiveData
import com.example.aimusicplayer.data.db.dao.UserDao
import com.example.aimusicplayer.data.model.User
import com.example.aimusicplayer.data.model.UserDetailResponse
import com.example.aimusicplayer.data.model.UserSubCountResponse
import com.example.aimusicplayer.data.source.ApiService
import com.example.aimusicplayer.network.ApiCallStrategy
import com.example.aimusicplayer.utils.NetworkResult
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.withContext
import okhttp3.ResponseBody
import org.json.JSONObject
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 用户数据仓库
 * 负责用户相关数据的获取和管理
 * 使用Hilt依赖注入
 */
@Singleton
class UserRepository @Inject constructor(
    private val context: Context,
    private val apiService: ApiService,
    private val sharedPreferences: SharedPreferences,
    private val userDao: UserDao
) : BaseRepository() {
    companion object {
        private const val TAG = "UserRepository"
        private const val PREF_NAME = "user_prefs"
        private const val KEY_USER_ID = "user_id"
        private const val KEY_USERNAME = "username"
        private const val KEY_AVATAR_URL = "avatar_url"
        private const val KEY_IS_LOGGED_IN = "is_logged_in"
        private const val KEY_TOKEN = "token"
        private const val KEY_USER_TOKEN = "user_token" // 兼容旧版本
        private const val KEY_COOKIE = "cookie" // 存储登录Cookie
    }

    // 当前用户 - 使用Flow
    private val _currentUserFlow = MutableStateFlow<User?>(null)
    val currentUserFlow: StateFlow<User?> = _currentUserFlow.asStateFlow()
    val currentUser: LiveData<User?> = currentUserFlow.asLiveData() // 兼容LiveData

    // 登录状态 - 使用Flow
    private val _loginStatusFlow = MutableStateFlow(false)
    val loginStatusFlow: StateFlow<Boolean> = _loginStatusFlow.asStateFlow()
    val loginStatus: LiveData<Boolean> = loginStatusFlow.asLiveData() // 兼容LiveData

    // 兼容旧版本的LiveData
    private val _currentUserLiveData = MutableLiveData<User?>()
    private val _loginStatusLiveData = MutableLiveData<Boolean>()

    init {
        // 从SharedPreferences加载用户信息
        loadUserFromPrefs()

        // 初始化登录状态
        _loginStatusFlow.value = isLoggedIn()
        _loginStatusLiveData.value = isLoggedIn()
    }

    /**
     * 从SharedPreferences加载用户信息
     */
    private fun loadUserFromPrefs() {
        val isLoggedIn = sharedPreferences.getBoolean(KEY_IS_LOGGED_IN, false)

        if (isLoggedIn) {
            val userId = sharedPreferences.getString(KEY_USER_ID, "") ?: ""
            val username = sharedPreferences.getString(KEY_USERNAME, "") ?: ""
            val avatarUrl = sharedPreferences.getString(KEY_AVATAR_URL, "") ?: ""
            var token = sharedPreferences.getString(KEY_TOKEN, "") ?: ""
            // 兼容旧版本
            if (token.isEmpty()) {
                token = sharedPreferences.getString(KEY_USER_TOKEN, "") ?: ""
            }

            val user = User(userId, username, token, avatarUrl)

            // 更新Flow
            _currentUserFlow.value = user
            // 兼容旧版本
            _currentUserLiveData.value = user
        } else {
            // 更新Flow
            _currentUserFlow.value = null
            // 兼容旧版本
            _currentUserLiveData.value = null
        }
    }

    /**
     * 保存用户信息到SharedPreferences
     */
    fun saveUserToPrefs(user: User?, isLoggedIn: Boolean) {
        val editor = sharedPreferences.edit()

        if (user != null && isLoggedIn) {
            editor.putString(KEY_USER_ID, user.userId)
            editor.putString(KEY_USERNAME, user.username)
            editor.putString(KEY_AVATAR_URL, user.avatarUrl)
            if (!user.token.isNullOrEmpty()) {
                editor.putString(KEY_TOKEN, user.token)
                editor.putString(KEY_USER_TOKEN, user.token) // 兼容旧版本
            }
            editor.putBoolean(KEY_IS_LOGGED_IN, true)
        } else {
            editor.remove(KEY_USER_ID)
            editor.remove(KEY_USERNAME)
            editor.remove(KEY_AVATAR_URL)
            editor.remove(KEY_TOKEN)
            editor.remove(KEY_USER_TOKEN)
            editor.putBoolean(KEY_IS_LOGGED_IN, false)
        }

        editor.apply()

        // 更新Flow
        _currentUserFlow.value = user
        _loginStatusFlow.value = isLoggedIn

        // 兼容旧版本
        _currentUserLiveData.value = user
        _loginStatusLiveData.value = isLoggedIn

        Log.d(TAG, "保存用户信息: ${user?.username ?: "null"}, 登录状态: $isLoggedIn")
    }

    /**
     * 保存Cookie
     */
    fun saveCookie(cookie: String) {
        val editor = sharedPreferences.edit()
        editor.putString(KEY_COOKIE, cookie)
        editor.apply()

        Log.d(TAG, "保存Cookie: ${cookie.length} 字符")
    }

    /**
     * 获取Cookie
     */
    fun getCookie(): String {
        return sharedPreferences.getString(KEY_COOKIE, "") ?: ""
    }

    /**
     * 保存用户Token
     */
    fun saveUserToken(token: String) {
        val editor = sharedPreferences.edit()
        editor.putString(KEY_TOKEN, token)
        editor.putString(KEY_USER_TOKEN, token) // 兼容旧版本
        editor.apply()

        // 如果当前用户不为空，更新token
        val user = _currentUserFlow.value
        if (user != null) {
            user.token = token
            // 更新Flow
            _currentUserFlow.value = user
            // 兼容旧版本
            _currentUserLiveData.value = user
        }
    }

    /**
     * 获取用户Token
     */
    fun getUserToken(): String {
        var token = sharedPreferences.getString(KEY_TOKEN, "") ?: ""
        if (token.isEmpty()) {
            token = sharedPreferences.getString(KEY_USER_TOKEN, "") ?: ""
        }
        return token
    }

    /**
     * 检查用户是否已登录
     */
    fun isLoggedIn(): Boolean {
        return sharedPreferences.getBoolean(KEY_IS_LOGGED_IN, false)
    }

    /**
     * 获取用户ID
     */
    fun getUserId(): String {
        return sharedPreferences.getString(KEY_USER_ID, "") ?: ""
    }

    /**
     * 获取用户名
     */
    fun getUsername(): String {
        return sharedPreferences.getString(KEY_USERNAME, "") ?: ""
    }

    /**
     * 获取用户头像URL
     */
    fun getAvatarUrl(): String {
        return sharedPreferences.getString(KEY_AVATAR_URL, "") ?: ""
    }

    /**
     * 保存登录状态和用户信息
     */
    fun saveLoginStatus(isLoggedIn: Boolean, token: String, userId: String, username: String) {
        val user = if (isLoggedIn) User(userId, username, token, "") else null
        saveUserToPrefs(user, isLoggedIn)
    }

    /**
     * 保存登录状态和用户信息
     */
    fun saveLoginStatus(userId: String, username: String, token: String, avatarUrl: String) {
        val user = User(userId, username, token, avatarUrl)
        saveUserToPrefs(user, true)
    }

    /**
     * 清除登录状态和用户信息
     */
    fun clearLoginStatus() {
        try {
            val editor = sharedPreferences.edit()
            editor.putBoolean(KEY_IS_LOGGED_IN, false)
            editor.putString(KEY_TOKEN, "")
            editor.putString(KEY_USER_TOKEN, "")
            editor.putString(KEY_USER_ID, "")
            editor.putString(KEY_USERNAME, "")
            editor.putString(KEY_AVATAR_URL, "")
            editor.putString(KEY_COOKIE, "")
            editor.apply()

            // 更新Flow
            _currentUserFlow.value = null
            _loginStatusFlow.value = false

            // 兼容旧版本
            _currentUserLiveData.value = null
            _loginStatusLiveData.value = false

            Log.d(TAG, "清除登录状态")
        } catch (e: Exception) {
            Log.e(TAG, "清除登录状态失败", e)
        }
    }

    /**
     * 将ResponseBody转换为String
     */
    private suspend fun ResponseBody.toStringBody(): String {
        return withContext(Dispatchers.IO) {
            <EMAIL>()
        }
    }

    /**
     * 获取二维码Key
     * @return Flow<NetworkResult<String>>
     */
    fun getQrKeyFlow(): Flow<NetworkResult<String>> = safeApiCall {
        apiService.getQrKey().toStringBody()
    }

    /**
     * 获取二维码Key
     * @return String
     */
    suspend fun getQrKey(): String {
        return withContext(Dispatchers.IO) {
            apiService.getQrKey().toStringBody()
        }
    }

    /**
     * 获取二维码图片
     * @return Flow<NetworkResult<String>>
     */
    fun getQrImageFlow(key: String): Flow<NetworkResult<String>> = safeApiCall {
        apiService.getQrImage(key).toStringBody()
    }

    /**
     * 获取二维码图片
     * @return String
     */
    suspend fun getQrImage(key: String): String {
        return withContext(Dispatchers.IO) {
            apiService.getQrImage(key).toStringBody()
        }
    }

    /**
     * 检查二维码状态
     * @return Flow<NetworkResult<String>>
     */
    fun checkQrStatusFlow(key: String): Flow<NetworkResult<String>> = safeApiCall {
        apiService.checkQrStatus(key).toStringBody()
    }

    /**
     * 检查二维码状态
     * @return String
     */
    suspend fun checkQrStatus(key: String): String {
        return withContext(Dispatchers.IO) {
            apiService.checkQrStatus(key).toStringBody()
        }
    }

    /**
     * 游客登录
     * @return Flow<NetworkResult<String>>
     */
    fun guestLoginFlow(timestamp: Long): Flow<NetworkResult<String>> = safeApiCall {
        apiService.guestLogin(timestamp).toStringBody()
    }

    /**
     * 游客登录
     * @return String
     */
    suspend fun guestLogin(timestamp: Long): String {
        return withContext(Dispatchers.IO) {
            apiService.guestLogin(timestamp).toStringBody()
        }
    }

    /**
     * 检查登录状态（智能API调用，频率控制）
     * @return Flow<NetworkResult<String>>
     */
    fun checkLoginStatusFlow(): Flow<NetworkResult<String>> = smartApiCall(
        apiType = ApiCallStrategy.API_TYPE_LOGIN_STATUS,
        cacheKey = "login_status_response",
        primaryCall = { apiService.checkLoginStatus().toStringBody() },
        defaultValue = "{\"code\":301,\"message\":\"未登录\"}"
    )

    /**
     * 检查登录状态
     * @return String
     */
    suspend fun checkLoginStatus(): String {
        return withContext(Dispatchers.IO) {
            apiService.checkLoginStatus().toStringBody()
        }
    }

    /**
     * 获取用户账号信息（智能API调用，缓存2分钟）
     * @return Flow<NetworkResult<String>>
     */
    fun getUserAccountFlow(): Flow<NetworkResult<String>> = smartApiCall(
        apiType = ApiCallStrategy.API_TYPE_USER_INFO,
        cacheKey = "user_account_info",
        primaryCall = { apiService.getUserAccount().toStringBody() },
        defaultValue = "{\"code\":301,\"message\":\"未登录\"}"
    )

    /**
     * 获取用户账号信息
     * @return String
     */
    suspend fun getUserAccount(): String {
        return withContext(Dispatchers.IO) {
            apiService.getUserAccount().toStringBody()
        }
    }

    /**
     * 发送验证码
     * @return Flow<NetworkResult<String>>
     */
    fun sendCaptchaFlow(phone: String): Flow<NetworkResult<String>> = safeApiCall {
        apiService.sendCaptcha(phone).toStringBody()
    }

    /**
     * 发送验证码
     * @return String
     */
    suspend fun sendCaptcha(phone: String): String {
        return withContext(Dispatchers.IO) {
            try {
                val response = apiService.sendCaptcha(phone).toStringBody()
                Log.d(TAG, "发送验证码响应: $response")
                response
            } catch (e: Exception) {
                Log.e(TAG, "发送验证码失败", e)
                throw e
            }
        }
    }



    /**
     * 使用验证码登录
     * @return Flow<NetworkResult<String>>
     */
    fun loginWithCaptchaFlow(phone: String, captcha: String): Flow<NetworkResult<String>> = safeApiCall {
        apiService.loginWithCaptcha(phone, captcha).toStringBody()
    }

    /**
     * 使用验证码登录
     * @return String
     */
    suspend fun loginWithCaptcha(phone: String, captcha: String): String {
        return withContext(Dispatchers.IO) {
            try {
                val response = apiService.loginWithCaptcha(phone, captcha).toStringBody()
                Log.d(TAG, "验证码登录响应: $response")

                // 解析响应，提取Cookie
                val jsonObject = JSONObject(response)
                val code = jsonObject.optInt("code")
                if (code == 200) {
                    val cookie = jsonObject.optString("cookie", "")
                    if (cookie.isNotEmpty()) {
                        saveCookie(cookie)
                        Log.d(TAG, "登录成功，已保存Cookie")
                    }
                }

                response
            } catch (e: Exception) {
                Log.e(TAG, "验证码登录失败", e)
                throw e
            }
        }
    }



    /**
     * 生成二维码
     * @param key 二维码key
     * @return String
     */
    suspend fun createQrCode(key: String): String {
        return withContext(Dispatchers.IO) {
            try {
                val response = apiService.getQrImage(key).toStringBody()
                Log.d(TAG, "生成二维码响应: $response")
                response
            } catch (e: Exception) {
                Log.e(TAG, "生成二维码失败", e)
                throw e
            }
        }
    }

    /**
     * 检查二维码扫码状态
     * @param key 二维码key
     * @return String
     */
    suspend fun checkQrCode(key: String): String {
        return withContext(Dispatchers.IO) {
            try {
                val response = apiService.checkQrStatus(key).toStringBody()
                Log.d(TAG, "检查二维码状态响应: $response")

                // 解析响应，如果登录成功则保存Cookie
                val jsonObject = JSONObject(response)
                val code = jsonObject.optInt("code")
                if (code == 803) { // 803表示授权登录成功
                    val cookie = jsonObject.optString("cookie", "")
                    if (cookie.isNotEmpty()) {
                        saveCookie(cookie)
                        Log.d(TAG, "二维码登录成功，已保存Cookie")
                    }
                }

                response
            } catch (e: Exception) {
                Log.e(TAG, "检查二维码状态失败", e)
                throw e
            }
        }
    }

    /**
     * 使用手机号密码登录 - 简化版本
     * @return String
     */
    suspend fun loginWithPhone(phone: String, password: String): String {
        return withContext(Dispatchers.IO) {
            apiService.loginWithPhone(phone, password).toStringBody()
        }
    }

    /**
     * 退出登录 - 简化版本
     * @return String
     */
    suspend fun logout(): String {
        val result = withContext(Dispatchers.IO) {
            try {
                apiService.logout().toStringBody()
            } catch (e: Exception) {
                Log.w(TAG, "退出登录API调用失败，但仍清除本地状态", e)
                "logout_failed"
            }
        }

        // 无论服务器响应如何，都清除本地用户信息
        clearLoginStatus()

        return result
    }

    /**
     * 获取用户详情 - 简化版本
     * @param uid 用户ID
     * @return NetworkResult<UserDetailResponse>
     */
    suspend fun getUserDetail(uid: String): NetworkResult<UserDetailResponse> {
        return try {
            val response = withContext(Dispatchers.IO) {
                apiService.getUserDetail(uid)
            }
            if (response.code == 200) {
                NetworkResult.Success(response)
            } else {
                NetworkResult.Error("获取用户详情失败: ${response.message ?: "未知错误"}")
            }
        } catch (e: Exception) {
            Log.e(TAG, "获取用户详情失败", e)
            NetworkResult.Error("获取用户详情失败: ${e.message}")
        }
    }

    /**
     * 获取用户信息统计 - 简化版本
     * @return NetworkResult<UserSubCountResponse>
     */
    suspend fun getUserSubCount(): NetworkResult<UserSubCountResponse> {
        return try {
            val response = withContext(Dispatchers.IO) {
                apiService.getUserSubCount()
            }
            if (response.code == 200) {
                NetworkResult.Success(response)
            } else {
                NetworkResult.Error("获取用户信息统计失败: ${response.message ?: "未知错误"}")
            }
        } catch (e: Exception) {
            Log.e(TAG, "获取用户信息统计失败", e)
            NetworkResult.Error("获取用户信息统计失败: ${e.message}")
        }
    }

    /**
     * 更新用户信息
     * @param userDetail 用户详情响应
     * @param subCount 用户信息统计响应
     * @return 更新后的用户对象
     */
    fun updateUserInfo(userDetail: UserDetailResponse?, @Suppress("UNUSED_PARAMETER") subCount: UserSubCountResponse?): User? {
        if (userDetail == null || userDetail.userProfile == null) {
            return null
        }

        val profile = userDetail.userProfile
        val user = User(
            userId = profile.userId.toString(),
            username = profile.nickname,
            token = getUserToken(),
            avatarUrl = profile.avatarUrl,
            isVip = profile.vipType > 0,
            level = userDetail.level,
            signature = profile.signature ?: "",
            followeds = profile.followeds,
            follows = profile.follows
        )

        // 更新Flow
        _currentUserFlow.value = _currentUserFlow.value?.copy(
            userId = user.userId,
            username = user.username,
            avatarUrl = user.avatarUrl
        )

        // 兼容旧版本
        _currentUserLiveData.value = _currentUserLiveData.value?.copy(
            userId = user.userId,
            username = user.username,
            avatarUrl = user.avatarUrl
        )

        // 保存到SharedPreferences
        val editor = sharedPreferences.edit()
        editor.putString(KEY_USER_ID, user.userId)
        editor.putString(KEY_USERNAME, user.username)
        editor.putString(KEY_AVATAR_URL, user.avatarUrl)
        editor.apply()

        return user
    }

    /**
     * 添加数据库相关方法
     */

    /**
     * 从数据库获取当前用户
     * @return Flow<UserEntity?>
     */
    fun getCurrentUserFromDb(): Flow<com.example.aimusicplayer.data.db.entity.UserEntity?> {
        return userDao.getCurrentUser()
    }

    /**
     * 保存用户到数据库
     * @param user 用户实体
     */
    suspend fun saveUserToDb(user: com.example.aimusicplayer.data.db.entity.UserEntity) {
        withContext(Dispatchers.IO) {
            userDao.insert(user)
        }
    }

    /**
     * 从数据库删除用户
     * @param user 用户实体
     */
    suspend fun deleteUserFromDb(user: com.example.aimusicplayer.data.db.entity.UserEntity) {
        withContext(Dispatchers.IO) {
            userDao.delete(user)
        }
    }

    /**
     * 清空数据库中的所有用户
     */
    suspend fun clearAllUsersFromDb() {
        withContext(Dispatchers.IO) {
            userDao.deleteAll()
        }
    }
}
