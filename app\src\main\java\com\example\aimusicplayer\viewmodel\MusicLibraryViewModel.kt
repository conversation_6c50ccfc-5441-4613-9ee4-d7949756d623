package com.example.aimusicplayer.viewmodel

import android.app.Application
import android.util.Log
import androidx.lifecycle.LiveData
import androidx.lifecycle.asLiveData
import androidx.lifecycle.viewModelScope
import com.example.aimusicplayer.data.model.Song
import com.example.aimusicplayer.data.repository.MusicRepository
import com.example.aimusicplayer.error.GlobalErrorHandler
import com.example.aimusicplayer.utils.NetworkResult
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import javax.inject.Inject

/**
 * 音乐库的ViewModel
 * 负责处理音乐库的业务逻辑
 * 使用Flow管理UI状态
 */
@HiltViewModel
class MusicLibraryViewModel @Inject constructor(
    application: Application,
    private val musicRepository: MusicRepository,
    errorHandler: GlobalErrorHandler
) : FlowViewModel(application) {

    init {
        // 设置错误处理器
        this.errorHandler = errorHandler
    }

    companion object {
        private const val TAG = "MusicLibraryViewModel"
    }

    // 本地音乐列表的StateFlow
    private val _localMusicListFlow = MutableStateFlow<List<Song>>(emptyList())
    val localMusicListFlow: StateFlow<List<Song>> = _localMusicListFlow.asStateFlow()
    val localMusicList: LiveData<List<Song>> = localMusicListFlow.asLiveData() // 兼容LiveData

    // 收藏歌曲列表的StateFlow
    private val _favoriteSongListFlow = MutableStateFlow<List<Song>>(emptyList())
    val favoriteSongListFlow: StateFlow<List<Song>> = _favoriteSongListFlow.asStateFlow()
    val favoriteSongList: LiveData<List<Song>> = favoriteSongListFlow.asLiveData() // 兼容LiveData

    // 我的歌单列表的StateFlow
    private val _myPlaylistListFlow = MutableStateFlow<List<Any>>(emptyList())
    val myPlaylistListFlow: StateFlow<List<Any>> = _myPlaylistListFlow.asStateFlow()
    val myPlaylistList: LiveData<List<Any>> = myPlaylistListFlow.asLiveData() // 兼容LiveData

    // 收藏歌单列表的StateFlow
    private val _favoritePlaylistListFlow = MutableStateFlow<List<Any>>(emptyList())
    val favoritePlaylistListFlow: StateFlow<List<Any>> = _favoritePlaylistListFlow.asStateFlow()
    val favoritePlaylistList: LiveData<List<Any>> = favoritePlaylistListFlow.asLiveData() // 兼容LiveData

    // 搜索关键词的StateFlow
    private val _searchKeywordFlow = MutableStateFlow<String>("")
    val searchKeywordFlow: StateFlow<String> = _searchKeywordFlow.asStateFlow()
    val searchKeyword: LiveData<String> = searchKeywordFlow.asLiveData() // 兼容LiveData

    // 搜索结果的StateFlow
    private val _searchResultFlow = MutableStateFlow<List<Song>>(emptyList())
    val searchResultFlow: StateFlow<List<Song>> = _searchResultFlow.asStateFlow()
    val searchResult: LiveData<List<Song>> = searchResultFlow.asLiveData() // 兼容LiveData

    init {
        // 初始化时加载数据
        loadAllData()
    }

    /**
     * 加载所有数据
     */
    fun loadAllData() {
        loadLocalMusic()
        loadFavoriteSongs()
        loadMyPlaylists()
        loadFavoritePlaylists()
    }

    /**
     * 加载本地音乐
     */
    fun loadLocalMusic() {
        launchSafely(
            onError = { e ->
                Log.e(TAG, "加载本地音乐失败", e)
                handleError(e, "加载本地音乐失败: ${e.message}")
            }
        ) {
            setLoading(true)
            try {
                // 从MusicRepository加载本地音乐
                musicRepository.getLocalSongs().collectLatest { result ->
                    when (result) {
                        is NetworkResult.Success -> {
                            _localMusicListFlow.value = result.data
                        }
                        is NetworkResult.Error -> {
                            handleError(Exception(result.message), result.message)
                        }
                        is NetworkResult.Loading -> {
                            // 加载中状态处理, FlowViewModel的launchSafely已处理
                        }
                    }
                }
            } finally {
                setLoading(false)
            }
        }
    }

    /**
     * 加载收藏歌曲
     */
    fun loadFavoriteSongs() {
        launchSafely(
            onError = { e ->
                Log.e(TAG, "加载收藏歌曲失败", e)
                handleError(e, "加载收藏歌曲失败: ${e.message}")
            }
        ) {
            setLoading(true)
            try {
                // 从MusicRepository加载收藏歌曲
                musicRepository.getLikedSongs().collectLatest { result ->
                    when (result) {
                        is NetworkResult.Success -> {
                            _favoriteSongListFlow.value = result.data
                        }
                        is NetworkResult.Error -> {
                            handleError(Exception(result.message), result.message)
                        }
                        is NetworkResult.Loading -> {
                            // 加载中状态处理
                        }
                    }
                }
            } finally {
                setLoading(false)
            }
        }
    }

    /**
     * 加载我的歌单
     */
    fun loadMyPlaylists() {
        launchSafely(
            onError = { e ->
                Log.e(TAG, "加载我的歌单失败", e)
                handleError(e, "加载我的歌单失败: ${e.message}")
            }
        ) {
            setLoading(true)
            try {
                // 从MusicRepository加载我的歌单
                val result = withContext(Dispatchers.IO) {
                    musicRepository.getUserPlaylists()
                }

                when (result) {
                    is NetworkResult.Success -> {
                        _myPlaylistListFlow.value = result.data
                    }
                    is NetworkResult.Error -> {
                        handleError(Exception(result.message), result.message)
                    }
                    is NetworkResult.Loading -> {
                        // 加载中状态处理
                    }
                }
            } finally {
                setLoading(false)
            }
        }
    }

    /**
     * 加载收藏歌单
     */
    fun loadFavoritePlaylists() {
        launchSafely(
            onError = { e ->
                Log.e(TAG, "加载收藏歌单失败", e)
                handleError(e, "加载收藏歌单失败: ${e.message}")
            }
        ) {
            setLoading(true)
            try {
                // 从MusicRepository加载收藏歌单
                val result = withContext(Dispatchers.IO) {
                    musicRepository.getCollectedPlaylists()
                }

                when (result) {
                    is NetworkResult.Success -> {
                        _favoritePlaylistListFlow.value = result.data
                    }
                    is NetworkResult.Error -> {
                        handleError(Exception(result.message), result.message)
                    }
                    is NetworkResult.Loading -> {
                        // 加载中状态处理
                    }
                }
            } finally {
                setLoading(false)
            }
        }
    }

    /**
     * 收藏或取消收藏歌曲
     * @param song 歌曲
     * @param isFavorite 是否收藏
     */
    fun toggleFavoriteSong(song: Song, isFavorite: Boolean) {
        launchSafely(
            onError = { e ->
                Log.e(TAG, "收藏或取消收藏歌曲失败", e)
                handleError(e, "收藏或取消收藏歌曲失败: ${e.message}")
            }
        ) {
            setLoading(true)
            try {
                // 调用MusicRepository收藏或取消收藏歌曲
                val result = withContext(Dispatchers.IO) {
                    if (isFavorite) {
                        musicRepository.likeSong(song.id)
                    } else {
                        musicRepository.unlikeSong(song.id)
                    }
                }

                if (result) {
                    // 刷新收藏歌曲列表
                    loadFavoriteSongs()
                } else {
                    handleError(Exception("操作失败"), "收藏或取消收藏歌曲失败")
                }
            } finally {
                setLoading(false)
            }
        }
    }

    /**
     * 搜索音乐库
     * @param keyword 搜索关键词
     */
    fun searchLibrary(keyword: String) {
        _searchKeywordFlow.value = keyword
        if (keyword.isEmpty()) {
            _searchResultFlow.value = emptyList()
            return
        }

        launchSafely(
            onError = { e ->
                Log.e(TAG, "搜索音乐库失败", e)
                handleError(e, "搜索音乐库失败: ${e.message}")
            }
        ) {
            setLoading(true)
            try {
                // 从MusicRepository搜索音乐库
                val result = withContext(Dispatchers.IO) {
                    musicRepository.searchSongs(keyword)
                }
                _searchResultFlow.value = result
            } catch (e: Exception) {
                handleError(e, "搜索失败: ${e.message}")
            } finally {
                setLoading(false)
            }
        }
    }
}
