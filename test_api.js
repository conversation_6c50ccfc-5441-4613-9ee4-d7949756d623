// 测试API返回数据结构
const https = require('https');

// 测试搜索API
function testCloudSearch() {
    const options = {
        hostname: '1355831898-4499wupl9z.ap-guangzhou.tencentscf.com',
        path: '/cloudsearch?keywords=周杰伦&type=1&limit=5',
        method: 'GET',
        headers: {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
    };

    const req = https.request(options, (res) => {
        let data = '';
        res.on('data', (chunk) => {
            data += chunk;
        });
        res.on('end', () => {
            console.log('=== 云搜索API返回结构 ===');
            try {
                const json = JSON.parse(data);
                console.log(JSON.stringify(json, null, 2));
            } catch (e) {
                console.log('解析JSON失败:', e.message);
                console.log('原始数据:', data);
            }
        });
    });

    req.on('error', (e) => {
        console.error('请求失败:', e.message);
    });

    req.end();
}

// 测试搜索建议API
function testSearchSuggest() {
    const options = {
        hostname: '1355831898-4499wupl9z.ap-guangzhou.tencentscf.com',
        path: '/search/suggest?keywords=周杰伦&type=mobile',
        method: 'GET',
        headers: {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
    };

    const req = https.request(options, (res) => {
        let data = '';
        res.on('data', (chunk) => {
            data += chunk;
        });
        res.on('end', () => {
            console.log('\n=== 搜索建议API返回结构 ===');
            try {
                const json = JSON.parse(data);
                console.log(JSON.stringify(json, null, 2));
            } catch (e) {
                console.log('解析JSON失败:', e.message);
                console.log('原始数据:', data);
            }
        });
    });

    req.on('error', (e) => {
        console.error('请求失败:', e.message);
    });

    req.end();
}

// 测试新歌速递API
function testTopSong() {
    const options = {
        hostname: '1355831898-4499wupl9z.ap-guangzhou.tencentscf.com',
        path: '/top/song?type=0',
        method: 'GET',
        headers: {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
    };

    const req = https.request(options, (res) => {
        let data = '';
        res.on('data', (chunk) => {
            data += chunk;
        });
        res.on('end', () => {
            console.log('\n=== 新歌速递API返回结构 ===');
            try {
                const json = JSON.parse(data);
                console.log(JSON.stringify(json, null, 2));
            } catch (e) {
                console.log('解析JSON失败:', e.message);
                console.log('原始数据:', data);
            }
        });
    });

    req.on('error', (e) => {
        console.error('请求失败:', e.message);
    });

    req.end();
}

// 执行测试
console.log('开始测试API数据结构...');
testCloudSearch();
setTimeout(testSearchSuggest, 2000);
setTimeout(testTopSong, 4000);
