package com.example.aimusicplayer.service

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.media.AudioAttributes
import android.media.AudioFocusRequest
import android.media.AudioManager
import android.os.Build
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.widget.Toast
import androidx.annotation.OptIn
import androidx.core.app.NotificationCompat
import androidx.core.content.ContextCompat
import androidx.media3.common.C
import androidx.media3.common.MediaItem
import androidx.media3.common.MediaMetadata
import androidx.media3.common.Player
import androidx.media3.common.PlaybackException
import androidx.media3.common.util.UnstableApi
import androidx.media3.exoplayer.ExoPlayer
import androidx.media3.exoplayer.source.DefaultMediaSourceFactory
import androidx.media3.session.DefaultMediaNotificationProvider
import androidx.media3.session.MediaSession
import androidx.media3.session.MediaSessionService
import com.bumptech.glide.Glide
import com.example.aimusicplayer.MusicApplication
import com.example.aimusicplayer.R
import com.example.aimusicplayer.data.source.MusicDataSource
import com.example.aimusicplayer.service.PlayMode
import com.example.aimusicplayer.ui.main.MainActivity
import com.example.aimusicplayer.utils.Constants
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.util.Random
import javax.inject.Inject

/**
 * 统一播放服务
 * 基于Media3的MediaSessionService实现
 * 负责音乐播放、播放队列管理、播放状态广播等
 */
@AndroidEntryPoint
class UnifiedPlaybackService : MediaSessionService() {

    companion object {
        // 播放控制广播Action
        const val ACTION_PLAY = "com.example.aimusicplayer.ACTION_PLAY"
        const val ACTION_PAUSE = "com.example.aimusicplayer.ACTION_PAUSE"
        const val ACTION_PREVIOUS = "com.example.aimusicplayer.ACTION_PREVIOUS"
        const val ACTION_NEXT = "com.example.aimusicplayer.ACTION_NEXT"
        const val ACTION_STOP = "com.example.aimusicplayer.ACTION_STOP"

        // 通知相关
        const val NOTIFICATION_ID = 1
        const val CHANNEL_ID = "music_playback_channel"

        val EXTRA_NOTIFICATION = "${UnifiedPlaybackService::class.java.packageName}.notification"
    }
    private val TAG = "UnifiedPlaybackService"
    private val serviceScope = CoroutineScope(SupervisorJob() + Dispatchers.Main)

    // 播放器和会话
    private lateinit var player: Player
    private lateinit var session: MediaSession
    private lateinit var notificationManager: NotificationManager

    // 播放模式
    private var playMode: PlayMode = PlayMode.Loop



    // 状态流
    private val playStateFlow = MutableStateFlow<PlayState>(PlayState.Idle)
    private val progressFlow = MutableStateFlow(0L)
    private val bufferingPercentFlow = MutableStateFlow(0)
    private val playModeFlow = MutableStateFlow<PlayMode>(PlayMode.Loop)
    private val currentSongFlow = MutableStateFlow<MediaItem?>(null)

    // 进度更新
    private val progressHandler = Handler(Looper.getMainLooper())
    private val progressRunnable = object : Runnable {
        override fun run() {
            if (player.isPlaying) {
                val position = player.currentPosition
                progressFlow.value = position
            }
            progressHandler.postDelayed(this, 500)
        }
    }

    // 播放控制广播接收器
    private val playbackControlReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            val action = intent.action ?: return
            when (action) {
                ACTION_PLAY -> play()
                ACTION_PAUSE -> pause()
                ACTION_PREVIOUS -> playPrevious()
                ACTION_NEXT -> playNext()
                ACTION_STOP -> stop()
            }
        }
    }

    // 播放监听器
    private var playbackListener: PlaybackListener? = null

    @Inject
    lateinit var musicDataSource: MusicDataSource

    @OptIn(UnstableApi::class)
    override fun onCreate() {
        super.onCreate()
        Log.d(TAG, "播放服务开始创建")

        try {
            // 注册服务实例到PlayServiceModule
            PlayServiceModule.setService(this)

            // 创建通知渠道
            createNotificationChannel()
            Log.d(TAG, "通知渠道创建完成")

            // 初始化通知管理器
            notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager

            // 异步初始化ExoPlayer，避免阻塞主线程
            serviceScope.launch(Dispatchers.Main) {
                try {
                    initializePlayer()
                    Log.d(TAG, "播放器初始化完成")
                } catch (e: Exception) {
                    Log.e(TAG, "播放器初始化失败", e)
                }
            }

            // 注册广播接收器
            registerPlaybackControlReceiver()

            // 开始进度更新
            progressHandler.post(progressRunnable)

            // 异步加载保存的播放模式
            serviceScope.launch(Dispatchers.IO) {
                try {
                    loadPlayMode()
                    // 在主线程设置播放模式
                    serviceScope.launch(Dispatchers.Main) {
                        setPlayMode(playMode)
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "加载播放模式失败", e)
                }
            }

            Log.d(TAG, "播放服务创建完成")
        } catch (e: Exception) {
            Log.e(TAG, "播放服务创建失败", e)
            throw e
        }
    }

    /**
     * 初始化播放器（异步执行）
     */
    @OptIn(UnstableApi::class)
    private suspend fun initializePlayer() = withContext(Dispatchers.Main) {
        try {
            player = ExoPlayer.Builder(applicationContext)
                // 参考ponymusic：启用自动音频焦点处理
                .setAudioAttributes(
                    androidx.media3.common.AudioAttributes.Builder()
                        .setUsage(androidx.media3.common.C.USAGE_MEDIA)
                        .setContentType(androidx.media3.common.C.AUDIO_CONTENT_TYPE_MUSIC)
                        .build(),
                    true
                )
                // 自动处理音频中断（如耳机拔出）
                .setHandleAudioBecomingNoisy(true)
                // 设置播放模式
                .setWakeMode(C.WAKE_MODE_NETWORK)
                .setMediaSourceFactory(
                    DefaultMediaSourceFactory(applicationContext)
                        .setDataSourceFactory(MusicDataSource.Factory(applicationContext))
                )
                .build()

            // 设置播放器监听器
            setupPlayerListeners()

            // 创建媒体会话
            session = MediaSession.Builder(this@UnifiedPlaybackService, player)
                .setSessionActivity(
                    PendingIntent.getActivity(
                        this@UnifiedPlaybackService,
                        0,
                        Intent(this@UnifiedPlaybackService, MainActivity::class.java).apply {
                            putExtra(EXTRA_NOTIFICATION, true)
                            putExtra("OPEN_PLAYER_FRAGMENT", true) // 添加标记，指示打开PlayerFragment
                            action = Intent.ACTION_VIEW
                            addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
                            addFlags(Intent.FLAG_ACTIVITY_SINGLE_TOP)
                            addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                        },
                        PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
                    )
                )
                .build()

            // 设置通知提供者 - Android Automotive优化
            setMediaNotificationProvider(
                DefaultMediaNotificationProvider.Builder(applicationContext)
                    .setChannelId(CHANNEL_ID)
                    .setNotificationId(NOTIFICATION_ID)
                    .build().apply {
                        setSmallIcon(R.drawable.ic_notification)
                    }
            )

            // 设置播放器到PlayServiceModule
            PlayServiceModule.setPlayer(player)

            Log.d(TAG, "ExoPlayer初始化成功")
        } catch (e: Exception) {
            Log.e(TAG, "ExoPlayer初始化失败", e)
            throw e
        }
    }

    /**
     * 注册播放控制广播接收器 - Android Automotive兼容
     */
    private fun registerPlaybackControlReceiver() {
        val filter = IntentFilter().apply {
            addAction(ACTION_PLAY)
            addAction(ACTION_PAUSE)
            addAction(ACTION_PREVIOUS)
            addAction(ACTION_NEXT)
            addAction(ACTION_STOP)
        }

        // Android 13+ 需要明确指定RECEIVER_EXPORTED
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            registerReceiver(playbackControlReceiver, filter, Context.RECEIVER_NOT_EXPORTED)
        } else {
            registerReceiver(playbackControlReceiver, filter)
        }
        Log.d(TAG, "播放控制广播接收器注册完成")
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        // 调用父类方法
        super.onStartCommand(intent, flags, startId)

        try {
            // 立即启动前台服务，避免ANR
            startForeground(NOTIFICATION_ID, createDefaultNotification())
            Log.d(TAG, "前台服务启动成功（使用默认通知）")

            // 等待播放器初始化完成后更新通知
            serviceScope.launch {
                // 等待播放器初始化完成
                var retryCount = 0
                while (!::player.isInitialized && retryCount < 50) { // 最多等待5秒
                    delay(100)
                    retryCount++
                }

                if (::player.isInitialized) {
                    // 更新通知
                    updateNotification()
                    Log.d(TAG, "播放器初始化完成，通知已更新")
                } else {
                    Log.e(TAG, "播放器初始化超时")
                }
            }

            // 处理播放控制Intent
            intent?.action?.let { action ->
                when (action) {
                    // 移除PLAY_INITIAL_SONG处理逻辑
                    ACTION_PLAY -> {
                        Log.d(TAG, "收到播放Intent")
                        serviceScope.launch {
                            // 等待播放器初始化
                            var retryCount = 0
                            while (!::player.isInitialized && retryCount < 50) {
                                delay(100)
                                retryCount++
                            }
                            if (::player.isInitialized) {
                                play()
                            }
                        }
                    }
                    ACTION_PAUSE -> {
                        Log.d(TAG, "收到暂停Intent")
                        if (::player.isInitialized) {
                            pause()
                        } else {
                            Log.w(TAG, "播放器未初始化，无法暂停")
                        }
                    }
                    ACTION_NEXT -> {
                        Log.d(TAG, "收到下一首Intent")
                        if (::player.isInitialized) {
                            playNext()
                        } else {
                            Log.w(TAG, "播放器未初始化，无法播放下一首")
                        }
                    }
                    ACTION_PREVIOUS -> {
                        Log.d(TAG, "收到上一首Intent")
                        if (::player.isInitialized) {
                            playPrevious()
                        } else {
                            Log.w(TAG, "播放器未初始化，无法播放上一首")
                        }
                    }
                    ACTION_STOP -> {
                        Log.d(TAG, "收到停止Intent")
                        if (::player.isInitialized) {
                            stop()
                        } else {
                            Log.w(TAG, "播放器未初始化，无法停止")
                        }
                    }
                    else -> {
                        Log.w(TAG, "未知的Intent action: $action")
                    }
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "onStartCommand执行失败", e)
        }

        return START_NOT_STICKY
    }

    /**
     * 设置播放器监听器
     */
    private fun setupPlayerListeners() {
        player.addListener(object : Player.Listener {
            override fun onMediaItemTransition(mediaItem: MediaItem?, reason: Int) {
                super.onMediaItemTransition(mediaItem, reason)
                mediaItem?.let {
                    // 当切换到新的歌曲时，加载歌词
                    loadLyrics(it.mediaId)

                    // 更新当前歌曲状态流
                    currentSongFlow.value = it

                    // 保存播放历史
                    savePlayHistory(it)

                    // 更新通知
                    updateNotification()
                }
            }

            override fun onPlaybackStateChanged(playbackState: Int) {
                super.onPlaybackStateChanged(playbackState)
                // 更新播放状态流
                when (playbackState) {
                    Player.STATE_IDLE -> {
                        playStateFlow.value = PlayState.Idle
                        progressFlow.value = 0
                        bufferingPercentFlow.value = 0
                    }
                    Player.STATE_BUFFERING -> {
                        playStateFlow.value = PlayState.Preparing
                        bufferingPercentFlow.value = player.bufferedPercentage
                    }
                    Player.STATE_READY -> {
                        playStateFlow.value = if (player.isPlaying) PlayState.Playing else PlayState.Pause
                    }
                    Player.STATE_ENDED -> {
                        // 根据播放模式处理播放结束
                        handlePlaybackEnded()
                    }
                }

                // 更新通知
                updateNotification()
            }

            override fun onIsPlayingChanged(isPlaying: Boolean) {
                super.onIsPlayingChanged(isPlaying)
                // 更新播放状态流
                if (player.playbackState == Player.STATE_READY) {
                    playStateFlow.value = if (isPlaying) PlayState.Playing else PlayState.Pause
                }

                // 通知播放状态变化
                playbackListener?.onPlayStateChanged(isPlaying)

                // 更新通知
                updateNotification()
            }

            override fun onPlaylistMetadataChanged(mediaMetadata: MediaMetadata) {
                super.onPlaylistMetadataChanged(mediaMetadata)
                // 播放列表元数据变化时的处理（如果需要）
            }

            // 监听缓冲位置变化
            override fun onEvents(player: Player, events: Player.Events) {
                super.onEvents(player, events)
                // 检查是否有缓冲位置变化事件
                if (events.contains(Player.EVENT_POSITION_DISCONTINUITY)) {
                    // 更新缓冲百分比
                    val bufferedPosition = player.bufferedPosition
                    if (player.duration > 0) {
                        bufferingPercentFlow.value = (bufferedPosition * 100 / player.duration).toInt()
                    }
                }
            }

            @OptIn(UnstableApi::class)
            override fun onPlayerError(@Suppress("UNUSED_PARAMETER") error: PlaybackException) {
                super.onPlayerError(error)
                Log.e(TAG, "播放错误: ${error.errorCodeName}, ${error.localizedMessage}")

                // 更新播放状态为错误
                playStateFlow.value = PlayState.Error("播放错误: ${error.errorCodeName}, ${error.localizedMessage}")

                // 通知播放错误
                playbackListener?.onError("播放错误: ${error.errorCodeName}, ${error.localizedMessage}")

                // 尝试恢复播放
                handlePlaybackError(error)
            }
        })
    }

    /**
     * 处理播放结束 - 简化版本，ExoPlayer会自动处理播放模式
     */
    private fun handlePlaybackEnded() {
        // ExoPlayer会根据设置的RepeatMode和ShuffleMode自动处理播放结束
        // 这里只需要处理特殊情况，如播放列表为空
        Log.d(TAG, "播放结束，ExoPlayer将根据播放模式自动处理")
    }

    /**
     * 处理播放错误 - 简化版本
     */
    @Suppress("UNUSED_PARAMETER")
    private fun handlePlaybackError(error: PlaybackException) {
        // 简单的错误处理，让ExoPlayer自己处理重试逻辑
        Log.e(TAG, "播放错误，ExoPlayer将自动处理重试")

        // 可以在这里添加用户通知或其他错误处理逻辑
        playbackListener?.onError("播放出现错误，正在尝试恢复...")
    }

    /**
     * 保存播放历史
     */
    private fun savePlayHistory(mediaItem: MediaItem) {
        serviceScope.launch(Dispatchers.IO) {
            try {
                val songId = mediaItem.mediaId.toLongOrNull() ?: return@launch

                // 保存播放历史到数据库
                musicDataSource.savePlayHistory(songId, System.currentTimeMillis())
            } catch (e: Exception) {
                Log.e(TAG, "保存播放历史失败", e)
            }
        }
    }

    /**
     * 加载歌词
     */
    private fun loadLyrics(mediaId: String) {
        val songId = mediaId.toLongOrNull() ?: return

        serviceScope.launch {
            try {
                // 异步加载歌词
                val lyric = musicDataSource.getLyric(songId)
                Log.d(TAG, "歌词加载成功: ${lyric?.lrc?.lyric?.take(100)}...")
            } catch (e: Exception) {
                Log.e(TAG, "歌词加载失败", e)
            }
        }
    }



    /**
     * 创建通知渠道 - Android Automotive优化
     */
    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                CHANNEL_ID,
                "音乐播放控制",
                NotificationManager.IMPORTANCE_HIGH // 提高重要性确保显示
            ).apply {
                description = "车载音乐播放器控制通知"
                setShowBadge(true) // 允许显示徽章
                lockscreenVisibility = Notification.VISIBILITY_PUBLIC
                // 车载环境禁用声音和震动
                setSound(null, null)
                enableVibration(false)
                enableLights(false)
                // 允许在锁屏和车载界面显示
                setBypassDnd(true)
            }

            notificationManager.createNotificationChannel(channel)
            Log.d(TAG, "通知渠道创建完成: $CHANNEL_ID")
        }
    }

    /**
     * 创建通知
     */
    private fun createNotification(): Notification {
        // 检查播放器是否已初始化
        if (!::player.isInitialized) {
            return createDefaultNotification()
        }

        // 获取当前播放的媒体项 - 使用ExoPlayer的当前媒体项
        val mediaItem = player.currentMediaItem

        // 创建通知意图
        val intent = Intent(this, MainActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_SINGLE_TOP
            putExtra("OPEN_PLAYER_FRAGMENT", true) // 添加标记，指示打开PlayerFragment
        }

        val pendingIntent = PendingIntent.getActivity(
            this,
            0,
            intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        // 创建播放/暂停按钮意图
        val playPauseIntent = Intent(this, UnifiedPlaybackService::class.java).apply {
            action = if (player.isPlaying) ACTION_PAUSE else ACTION_PLAY
        }

        val playPausePendingIntent = PendingIntent.getService(
            this,
            1,
            playPauseIntent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        // 创建上一首按钮意图
        val prevIntent = Intent(this, UnifiedPlaybackService::class.java).apply {
            action = ACTION_PREVIOUS
        }

        val prevPendingIntent = PendingIntent.getService(
            this,
            2,
            prevIntent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        // 创建下一首按钮意图
        val nextIntent = Intent(this, UnifiedPlaybackService::class.java).apply {
            action = ACTION_NEXT
        }

        val nextPendingIntent = PendingIntent.getService(
            this,
            3,
            nextIntent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        // 创建通知 - Android Automotive优化，添加专辑封面
        val builder = NotificationCompat.Builder(this, CHANNEL_ID)
            .setSmallIcon(R.drawable.ic_notification)
            .setContentTitle(mediaItem?.mediaMetadata?.title ?: "轻聆音乐")
            .setContentText(mediaItem?.mediaMetadata?.artist ?: "正在播放音乐")
            .setSubText(mediaItem?.mediaMetadata?.albumTitle ?: "") // 添加专辑信息
            .setContentIntent(pendingIntent)
            .setVisibility(NotificationCompat.VISIBILITY_PUBLIC)
            .setPriority(NotificationCompat.PRIORITY_HIGH) // 提高优先级确保显示
            .setOnlyAlertOnce(true)
            .setOngoing(true)
            .setShowWhen(false) // 车载环境不显示时间
            .setAutoCancel(false) // 不允许滑动删除
            .setDeleteIntent(null) // 禁用删除意图
            .addAction(
                R.drawable.ic_previous,
                "上一首",
                prevPendingIntent
            )
            .addAction(
                if (player.isPlaying) R.drawable.ic_pause else R.drawable.ic_play,
                if (player.isPlaying) "暂停" else "播放",
                playPausePendingIntent
            )
            .addAction(
                R.drawable.ic_next,
                "下一首",
                nextPendingIntent
            )
            // 使用MediaStyle，符合车载要求
            .setStyle(
                androidx.media.app.NotificationCompat.MediaStyle()
                    .setMediaSession(session.sessionCompatToken)
                    .setShowActionsInCompactView(0, 1, 2) // 显示所有三个按钮
                    .setShowCancelButton(false) // 禁用取消按钮
            )

        // 异步加载专辑封面
        loadNotificationAlbumArt(builder, mediaItem)

        return builder.build()
    }

    /**
     * 创建默认通知（播放器未初始化时使用）
     */
    private fun createDefaultNotification(): Notification {
        // 创建通知意图
        val intent = Intent(this, MainActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_SINGLE_TOP
            putExtra("OPEN_PLAYER_FRAGMENT", true)
        }

        val pendingIntent = PendingIntent.getActivity(
            this,
            0,
            intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        // 创建默认通知 - Android Automotive优化
        val builder = NotificationCompat.Builder(this, CHANNEL_ID)
            .setSmallIcon(R.drawable.ic_notification)
            .setContentTitle("轻聆音乐")
            .setContentText("正在初始化播放服务...")
            .setContentIntent(pendingIntent)
            .setVisibility(NotificationCompat.VISIBILITY_PUBLIC)
            .setPriority(NotificationCompat.PRIORITY_DEFAULT)
            .setOnlyAlertOnce(true)
            .setOngoing(true)
            .setShowWhen(false)
            .setStyle(
                androidx.media.app.NotificationCompat.MediaStyle()
                    .setShowActionsInCompactView() // 初始化时不显示操作按钮
            )

        return builder.build()
    }

    /**
     * 异步加载通知栏专辑封面
     */
    private fun loadNotificationAlbumArt(builder: NotificationCompat.Builder, mediaItem: MediaItem?) {
        serviceScope.launch {
            try {
                val artworkUri = mediaItem?.mediaMetadata?.artworkUri
                if (artworkUri != null) {
                    // 使用Glide加载专辑封面
                    val bitmap = withContext(Dispatchers.IO) {
                        try {
                            Glide.with(applicationContext)
                                .asBitmap()
                                .load(artworkUri)
                                .override(256, 256) // 限制尺寸，优化性能
                                .centerCrop()
                                .submit()
                                .get(3000, java.util.concurrent.TimeUnit.MILLISECONDS) // 3秒超时
                        } catch (e: Exception) {
                            Log.w(TAG, "加载通知栏专辑封面失败", e)
                            null
                        }
                    }

                    // 在主线程更新通知
                    withContext(Dispatchers.Main) {
                        if (bitmap != null) {
                            builder.setLargeIcon(bitmap)
                            // 重新发布通知
                            notificationManager.notify(NOTIFICATION_ID, builder.build())
                            Log.d(TAG, "通知栏专辑封面加载成功")
                        }
                    }
                } else {
                    Log.d(TAG, "无专辑封面URI，使用默认图标")
                }
            } catch (e: Exception) {
                Log.e(TAG, "加载通知栏专辑封面异常", e)
            }
        }
    }

    /**
     * 更新通知
     */
    private fun updateNotification() {
        try {
            if (::player.isInitialized && ::session.isInitialized) {
                notificationManager.notify(NOTIFICATION_ID, createNotification())
            } else {
                notificationManager.notify(NOTIFICATION_ID, createDefaultNotification())
            }
        } catch (e: Exception) {
            Log.e(TAG, "更新通知失败", e)
        }
    }

    /**
     * 加载播放模式
     */
    private fun loadPlayMode() {
        val sharedPreferences = getSharedPreferences(Constants.PREF_NAME, Context.MODE_PRIVATE)
        val mode = sharedPreferences.getInt(Constants.PREF_PLAY_MODE, PlayMode.Loop.value)
        playMode = PlayMode.valueOf(mode)
        playModeFlow.value = playMode
    }

    /**
     * 保存播放模式
     */
    private fun savePlayMode(mode: PlayMode) {
        val sharedPreferences = getSharedPreferences(Constants.PREF_NAME, Context.MODE_PRIVATE)
        sharedPreferences.edit().putInt(Constants.PREF_PLAY_MODE, mode.value).apply()
    }

    /**
     * 设置播放模式
     * @param mode 播放模式
     */
    fun setPlayMode(mode: PlayMode) {
        this.playMode = mode

        // 根据播放模式设置ExoPlayer的重复模式和随机播放
        when (mode) {
            is PlayMode.Loop -> {
                // 列表循环，播放完列表后从头开始
                player.repeatMode = Player.REPEAT_MODE_ALL
                player.shuffleModeEnabled = false
            }
            is PlayMode.Single -> {
                // 单曲循环，重复播放当前歌曲
                player.repeatMode = Player.REPEAT_MODE_ONE
                player.shuffleModeEnabled = false
            }
            is PlayMode.Shuffle -> {
                // 随机播放，随机顺序播放列表中的歌曲
                player.repeatMode = Player.REPEAT_MODE_ALL
                player.shuffleModeEnabled = true
            }
        }

        // 保存播放模式
        savePlayMode(mode)

        // 更新状态流
        playModeFlow.value = mode

        // 通知播放模式变化
        playbackListener?.onPlayModeChanged(mode)
    }

    /**
     * 切换播放模式
     */
    fun togglePlayMode() {
        val nextMode = when (playMode) {
            is PlayMode.Loop -> PlayMode.Single
            is PlayMode.Single -> PlayMode.Shuffle
            is PlayMode.Shuffle -> PlayMode.Loop
        }

        setPlayMode(nextMode)
    }

    /**
     * 播放 - 简化版本，使用ExoPlayer的播放控制
     */
    fun play() {
        player.play()
    }

    /**
     * 暂停
     */
    fun pause() {
        player.pause()
    }

    /**
     * 播放/暂停切换
     */
    fun togglePlayPause() {
        if (player.isPlaying) {
            player.pause()
        } else {
            player.play()
        }
    }

    /**
     * 停止
     */
    fun stop() {
        player.stop()
    }

    /**
     * 播放下一首 - 使用ExoPlayer的内置功能
     */
    fun playNext() {
        player.seekToNext()
    }

    /**
     * 播放上一首 - 使用ExoPlayer的内置功能
     */
    fun playPrevious() {
        player.seekToPrevious()
    }

    /**
     * 播放指定索引的歌曲 - 使用ExoPlayer的内置功能
     */
    fun playAtIndex(index: Int) {
        player.seekTo(index, 0)
        player.prepare()
        player.play()
    }

    /**
     * 跳转到指定位置
     */
    fun seekTo(position: Long) {
        player.seekTo(position)
    }

    /**
     * 设置播放列表 - 简化版本，使用ExoPlayer的内置播放列表管理
     */
    fun setPlaylist(items: List<MediaItem>, startIndex: Int = 0) {
        // 直接使用ExoPlayer的播放列表管理
        player.setMediaItems(items, startIndex, 0)
        player.prepare()
        player.play()

        // 通知播放列表变化
        playbackListener?.onPlaylistChanged(items)

        // 保存播放列表到数据库
        savePlaylistToDatabase(items)
    }

    /**
     * 保存播放列表到数据库 - 简化版本
     */
    private fun savePlaylistToDatabase(items: List<MediaItem>) {
        serviceScope.launch(Dispatchers.IO) {
            try {
                // 将MediaItem列表转换为SongEntity列表
                val songEntities = items.mapNotNull { mediaItem ->
                    try {
                        val songId = mediaItem.mediaId.toLongOrNull() ?: return@mapNotNull null
                        val metadata = mediaItem.mediaMetadata

                        com.example.aimusicplayer.data.db.entity.SongEntity(
                            type = com.example.aimusicplayer.data.db.entity.SongEntity.TYPE_ONLINE,
                            songId = songId,
                            title = metadata.title?.toString() ?: "",
                            artist = metadata.artist?.toString() ?: "",
                            album = metadata.albumTitle?.toString() ?: "",
                            albumCover = metadata.artworkUri?.toString() ?: "",
                            duration = metadata.extras?.getLong("duration") ?: 0,
                            uri = mediaItem.requestMetadata.mediaUri?.toString() ?: ""
                        )
                    } catch (e: Exception) {
                        Log.e(TAG, "转换MediaItem到SongEntity失败", e)
                        null
                    }
                }

                // 保存到数据库
                if (songEntities.isNotEmpty()) {
                    musicDataSource.savePlaylist(songEntities)
                }
            } catch (e: Exception) {
                Log.e(TAG, "保存播放列表到数据库失败", e)
            }
        }
    }

    /**
     * 添加到播放列表 - 简化版本，使用ExoPlayer的内置功能
     */
    fun addToPlaylist(item: MediaItem) {
        player.addMediaItem(item)

        // 获取当前播放列表并通知变化
        val currentPlaylist = getCurrentPlaylistFromPlayer()
        playbackListener?.onPlaylistChanged(currentPlaylist)

        // 保存播放列表到数据库
        savePlaylistToDatabase(currentPlaylist)
    }

    /**
     * 添加到播放列表 - 简化版本，使用ExoPlayer的内置功能
     */
    fun addToPlaylist(items: List<MediaItem>) {
        player.addMediaItems(items)

        // 获取当前播放列表并通知变化
        val currentPlaylist = getCurrentPlaylistFromPlayer()
        playbackListener?.onPlaylistChanged(currentPlaylist)

        // 保存播放列表到数据库
        savePlaylistToDatabase(currentPlaylist)
    }

    /**
     * 从ExoPlayer获取当前播放列表
     */
    private fun getCurrentPlaylistFromPlayer(): List<MediaItem> {
        val timeline = player.currentTimeline
        val mediaItems = mutableListOf<MediaItem>()

        for (i in 0 until timeline.windowCount) {
            val window = androidx.media3.common.Timeline.Window()
            timeline.getWindow(i, window)
            mediaItems.add(window.mediaItem)
        }

        return mediaItems
    }

    /**
     * 添加并播放 - 简化版本，使用ExoPlayer的内置功能
     */
    fun addAndPlay(item: MediaItem) {
        // 检查是否已在播放列表中
        val currentPlaylist = getCurrentPlaylistFromPlayer()
        val index = currentPlaylist.indexOfFirst { it.mediaId == item.mediaId }

        if (index >= 0) {
            // 如果已在播放列表中，直接播放
            player.seekTo(index, 0)
            player.prepare()
            player.play()
        } else {
            // 添加到播放列表并播放
            player.addMediaItem(item)
            val newIndex = player.mediaItemCount - 1
            player.seekTo(newIndex, 0)
            player.prepare()
            player.play()

            // 通知播放列表变化
            val updatedPlaylist = getCurrentPlaylistFromPlayer()
            playbackListener?.onPlaylistChanged(updatedPlaylist)

            // 保存播放列表到数据库
            savePlaylistToDatabase(updatedPlaylist)
        }
    }

    /**
     * 从播放列表中移除 - 简化版本，使用ExoPlayer的内置功能
     */
    fun removeFromPlaylist(index: Int) {
        if (index >= 0 && index < player.mediaItemCount) {
            // 获取要删除的歌曲
            val removedItem = player.getMediaItemAt(index)

            // 从ExoPlayer播放列表中移除
            player.removeMediaItem(index)

            // 通知播放列表变化
            val updatedPlaylist = getCurrentPlaylistFromPlayer()
            playbackListener?.onPlaylistChanged(updatedPlaylist)

            // 从数据库中删除
            serviceScope.launch(Dispatchers.IO) {
                try {
                    val songId = removedItem.mediaId.toLongOrNull() ?: return@launch
                    musicDataSource.removeFromPlaylist(songId)
                } catch (e: Exception) {
                    Log.e(TAG, "从数据库删除歌曲失败", e)
                }
            }
        }
    }

    /**
     * 清空播放列表 - 简化版本，使用ExoPlayer的内置功能
     */
    fun clearPlaylist() {
        // 清空ExoPlayer播放列表
        player.clearMediaItems()
        player.stop()

        // 更新当前歌曲状态流
        currentSongFlow.value = null

        // 通知播放列表变化
        playbackListener?.onPlaylistChanged(emptyList())

        // 清空数据库中的播放列表
        serviceScope.launch(Dispatchers.IO) {
            try {
                musicDataSource.clearPlaylist()
            } catch (e: Exception) {
                Log.e(TAG, "清空数据库播放列表失败", e)
            }
        }
    }

    /**
     * 获取播放列表 - 简化版本，从ExoPlayer获取
     */
    fun getPlaylist(): List<MediaItem> {
        return getCurrentPlaylistFromPlayer()
    }

    /**
     * 获取当前索引 - 简化版本，从ExoPlayer获取
     */
    fun getCurrentIndex(): Int {
        return player.currentMediaItemIndex
    }

    /**
     * 获取当前播放状态
     */
    fun getPlayState(): StateFlow<PlayState> {
        return playStateFlow
    }

    /**
     * 获取当前播放进度
     */
    fun getProgress(): StateFlow<Long> {
        return progressFlow
    }

    /**
     * 获取当前缓冲百分比
     */
    fun getBufferingPercent(): StateFlow<Int> {
        return bufferingPercentFlow
    }

    /**
     * 获取当前播放模式
     */
    fun getPlayMode(): StateFlow<PlayMode> {
        return playModeFlow
    }

    /**
     * 获取当前歌曲
     */
    fun getCurrentSong(): StateFlow<MediaItem?> {
        return currentSongFlow
    }



    /**
     * 设置播放监听器
     */
    fun setPlaybackListener(listener: PlaybackListener?) {
        this.playbackListener = listener
    }

    /**
     * 启动心动模式
     * @param songId 歌曲ID
     * @param playlistId 歌单ID，可选
     */
    fun startIntelligenceMode(songId: Long, playlistId: Long? = null) {
        serviceScope.launch {
            try {
                // 显示加载提示
                playbackListener?.onLoading(true, "正在加载心动模式...")

                // 调用API获取心动模式歌曲列表
                val result = musicDataSource.getIntelligenceList(songId, playlistId)

                if (result.isNotEmpty()) {
                    // 转换为MediaItem列表
                    val mediaItems = result.mapNotNull { song ->
                        try {
                            val metadata = MediaMetadata.Builder()
                                .setTitle(song.title)
                                .setArtist(song.artist)
                                .setAlbumTitle(song.album)
                                .setArtworkUri(android.net.Uri.parse(song.albumCover))
                                .setMediaType(MediaMetadata.MEDIA_TYPE_MUSIC)
                                .setIsBrowsable(false)
                                .setIsPlayable(true)
                                .build()

                            MediaItem.Builder()
                                .setMediaId(song.songId.toString())
                                .setMediaMetadata(metadata)
                                .setUri(song.uri)
                                .build()
                        } catch (e: Exception) {
                            Log.e(TAG, "转换歌曲到MediaItem失败", e)
                            null
                        }
                    }

                    if (mediaItems.isNotEmpty()) {
                        // 设置播放列表并开始播放
                        setPlaylist(mediaItems)

                        // 通知心动模式启动成功
                        playbackListener?.onIntelligenceModeStarted(mediaItems)
                    } else {
                        // 通知心动模式启动失败
                        playbackListener?.onError("心动模式启动失败：无法转换歌曲")
                    }
                } else {
                    // 通知心动模式启动失败
                    playbackListener?.onError("心动模式启动失败：未找到相似歌曲")
                }
            } catch (e: Exception) {
                Log.e(TAG, "心动模式启动失败", e)
                // 通知心动模式启动失败
                playbackListener?.onError("心动模式启动失败：${e.message}")
            } finally {
                // 隐藏加载提示
                playbackListener?.onLoading(false)
            }
        }
    }

    /**
     * 播放状态监听器接口
     */
    interface PlaybackListener {
        /**
         * 播放状态变化回调
         * @param isPlaying 是否正在播放
         */
        fun onPlayStateChanged(isPlaying: Boolean)

        /**
         * 播放位置变化回调
         * @param position 当前位置
         * @param duration 总时长
         */
        fun onPositionChanged(position: Long, duration: Long)

        /**
         * 播放模式变化回调
         * @param mode 播放模式
         */
        fun onPlayModeChanged(mode: PlayMode)

        /**
         * 播放列表变化回调
         * @param playlist 播放列表
         */
        fun onPlaylistChanged(playlist: List<MediaItem>)

        /**
         * 当前播放歌曲变化回调
         * @param mediaItem 当前播放的媒体项
         * @param position 在播放列表中的位置
         */
        fun onCurrentMediaItemChanged(mediaItem: MediaItem, position: Int)

        /**
         * 播放错误回调
         * @param error 错误信息
         */
        fun onError(error: String)

        /**
         * 加载状态回调
         * @param isLoading 是否正在加载
         * @param message 加载提示信息
         */
        fun onLoading(isLoading: Boolean, message: String = "")

        /**
         * 心动模式启动成功回调
         * @param playlist 心动模式播放列表
         */
        fun onIntelligenceModeStarted(playlist: List<MediaItem>)
    }

    override fun onGetSession(controllerInfo: MediaSession.ControllerInfo): MediaSession? {
        return session
    }

    override fun onTaskRemoved(rootIntent: Intent?) {
        super.onTaskRemoved(rootIntent)
        // 当应用被从任务列表移除时，停止播放
        player.stop()
    }

    override fun onDestroy() {
        super.onDestroy()
        // 停止进度更新
        progressHandler.removeCallbacks(progressRunnable)

        // 注销广播接收器
        try {
            unregisterReceiver(playbackControlReceiver)
        } catch (e: Exception) {
            Log.e(TAG, "注销广播接收器失败", e)
        }

        // ExoPlayer会自动处理音频焦点释放

        // 清理PlayServiceModule中的实例
        PlayServiceModule.clearInstances()

        // 释放资源
        player.release()
        session.release()
        Log.d(TAG, "播放服务已销毁")
    }

    // 已在类顶部定义了companion object，此处不需要重复定义
}
