package com.example.aimusicplayer.data.repository

import android.util.Log
import androidx.media3.common.MediaItem
import com.example.aimusicplayer.data.model.Album
import com.example.aimusicplayer.data.model.Artist
import com.example.aimusicplayer.data.model.Banner
import com.example.aimusicplayer.data.model.Comment
import com.example.aimusicplayer.data.model.PlayList
import com.example.aimusicplayer.data.model.Song
import com.example.aimusicplayer.data.model.User
import com.example.aimusicplayer.data.db.entity.SongEntity
import com.example.aimusicplayer.data.source.MusicDataSource
import com.example.aimusicplayer.utils.NetworkResult
import com.example.aimusicplayer.data.repository.BaseRepository
import com.example.aimusicplayer.network.ApiCallStrategy
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.withContext
import java.util.concurrent.ConcurrentHashMap
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 音乐仓库
 * 负责处理音乐数据的获取和缓存
 */
@Singleton
class MusicRepository @Inject constructor(
    private val musicDataSource: MusicDataSource
) : BaseRepository() {

    /**
     * 获取新歌速递 - 简化版本
     * @param type 地区类型 id，全部:0, 华语:7, 欧美:96, 日本:8, 韩国:16
     * @return List<Song>
     */
    suspend fun getNewSongs(type: Int = 0): List<Song> {
        return musicDataSource.getNewSongs(type)
    }

    /**
     * 获取新歌速递并转换为MediaItem
     * @param type 地区类型 id，全部:0, 华语:7, 欧美:96, 日本:8, 韩国:16
     * @return List<MediaItem>
     */
    suspend fun getNewSongsAsMediaItems(type: Int = 0): List<MediaItem> = withContext(Dispatchers.IO) {
        val songs = musicDataSource.getNewSongs(type)
        return@withContext songs.map { musicDataSource.songToMediaItem(it) }
    }

    /**
     * 获取歌曲详情 - 简化版本
     * @param id 歌曲ID
     * @return Song?
     */
    suspend fun getSongDetail(id: Long): Song? {
        return musicDataSource.getSongDetail(id)
    }

    /**
     * 获取歌词 - 简化版本
     * @param id 歌曲ID
     * @return LyricResponse?
     */
    suspend fun getLyric(id: Long): com.example.aimusicplayer.data.model.LyricResponse? {
        return musicDataSource.getLyric(id)
    }
/**
     * 获取Banner
     * @return Flow<NetworkResult<BaseResponse>>
     */
    suspend fun getBanners(): NetworkResult<List<com.example.aimusicplayer.data.model.Banner>> {
        // 注意：这里的 BaseResponse 可能需要根据实际的 Banner 数据结构进行转换
        // 假设 BaseResponse 包含一个 banners 列表
        @Suppress("UNUSED_VARIABLE")
        val response = musicDataSource.getBanners()
        // 这里需要根据 BaseResponse 的实际结构来提取 Banner 列表
        // 例如，如果 BaseResponse 有一个 banners 字段：
        // return NetworkResult.Success(response.banners)
        // 此处暂时返回一个空的成功响应，需要根据实际情况修改
        return NetworkResult.Success(emptyList())
    }
/**
     * 获取推荐歌单
     * @return Flow<NetworkResult<BaseResponse>>
     */
    suspend fun getRecommendPlaylists(): NetworkResult<List<PlayList>> {
        // 注意：这里的 BaseResponse 可能需要根据实际的 PlayList 数据结构进行转换
        @Suppress("UNUSED_VARIABLE")
        val response = musicDataSource.getRecommendPlaylists()
        // TODO: Implement proper conversion from BaseResponse to List<PlayList>
        // For now, returning an empty list as a placeholder.
        // Assuming BaseResponse has a 'result' or 'playlists' field that contains List<PlayList>
        // Example: return NetworkResult.Success(response.result ?: emptyList())
        return NetworkResult.Success(emptyList())
    }

    /**
     * 获取所有榜单
     * @return Flow<NetworkResult<BaseResponse>>
     */
    suspend fun getToplists(): NetworkResult<List<com.example.aimusicplayer.data.model.PlayList>> {
        // 注意：这里的 BaseResponse 可能需要根据实际的 PlayList 数据结构进行转换
        @Suppress("UNUSED_VARIABLE")
        val response = musicDataSource.getToplists()
        // 此处暂时返回一个空的成功响应，需要根据实际情况修改
        return NetworkResult.Success(emptyList())
    }

    /**
     * 获取新碟上架
     * @return Flow<NetworkResult<BaseResponse>>
     */
    suspend fun getNewAlbums(): NetworkResult<List<com.example.aimusicplayer.data.model.Album>> {
        // 注意：这里的 BaseResponse 可能需要根据实际的 Album 数据结构进行转换
        @Suppress("UNUSED_VARIABLE")
        val response = musicDataSource.getNewAlbums()
        // 此处暂时返回一个空的成功响应，需要根据实际情况修改
        return NetworkResult.Success(emptyList())
    }
/**
     * 获取推荐歌曲
     * @return Flow<NetworkResult<SongDetailResponse>>
     */
    suspend fun getRecommendedSongs(): NetworkResult<List<Song>> {
        // 注意：这里的 SongDetailResponse 可能需要根据实际的 Song 数据结构进行转换
        val response = musicDataSource.getRecommendedSongs()
        // Assuming SongDetailResponse has a 'songs' field
        return NetworkResult.Success(response.songs)
    }

    /**
     * 将Song对象转换为MediaItem
     * @param song 歌曲对象
     * @return MediaItem
     */
    fun songToMediaItem(song: Song): MediaItem {
        return musicDataSource.songToMediaItem(song)
    }

    /**
     * 获取MusicDataSource实例，用于直接访问数据源方法
     * @return MusicDataSource
     */
    fun getMusicDataSource(): MusicDataSource {
        return musicDataSource
    }

    /**
     * 获取评论列表 - 简化版本
     * @param songId 歌曲ID
     * @param offset 偏移量
     * @param limit 数量限制
     * @return List<Comment>
     */
    suspend fun getComments(songId: Long, offset: Int = 0, limit: Int = 50): List<Comment> {
        return withContext(Dispatchers.IO) {
            musicDataSource.getComments(songId, offset, limit)
        }
    }

    /**
     * 发送评论 - 简化版本
     * @param songId 歌曲ID
     * @param content 评论内容
     * @return Boolean
     */
    suspend fun sendComment(songId: Long, content: String): Boolean {
        return withContext(Dispatchers.IO) {
            musicDataSource.sendComment(songId, content)
        }
    }

    /**
     * 点赞评论 - 简化版本
     * @param commentId 评论ID
     * @return 是否成功
     */
    suspend fun likeComment(commentId: Long): Boolean {
        return withContext(Dispatchers.IO) {
            musicDataSource.likeComment(commentId)
        }
    }

    /**
     * 获取热门评论
     * @param songId 歌曲ID
     * @param limit 数量限制
     * @param forceRefresh 是否强制刷新
     * @return Flow<NetworkResult<List<Comment>>>
     */
    fun getHotCommentsFlow(songId: Long, limit: Int = 20, forceRefresh: Boolean = false): Flow<NetworkResult<List<Comment>>> = cachedApiCall(
        cacheKey = "hot_comments_${songId}_$limit",
        forceRefresh = forceRefresh,
        apiCall = { musicDataSource.getHotComments(songId, limit) }
    )

    /**
     * 获取热门评论
     * @param songId 歌曲ID
     * @param limit 数量限制
     * @param forceRefresh 是否强制刷新
     * @return 热门评论列表
     */
    suspend fun getHotComments(songId: Long, limit: Int = 20, forceRefresh: Boolean = false): List<Comment> {
        return withContext(Dispatchers.IO) {
            // 使用BaseRepository的缓存机制
            val cacheKey = "hot_comments_${songId}_$limit"
            val cachedData = getFromCache<List<Comment>>(cacheKey)
            val currentTime = System.currentTimeMillis()

            if (!forceRefresh && cachedData != null && (currentTime - cachedData.second < CACHE_EXPIRATION_TIME)) {
                // 使用缓存数据
                return@withContext cachedData.first
            }

            // 从网络获取数据
            val comments = musicDataSource.getHotComments(songId, limit)

            // 更新缓存
            putInCache(cacheKey, comments)

            comments
        }
    }

    /**
     * 检查歌曲收藏状态
     * @param songId 歌曲ID
     * @return Flow<NetworkResult<Boolean>>
     */
    fun checkLikeStatusFlow(songId: Long): Flow<NetworkResult<Boolean>> = safeApiCall {
        musicDataSource.checkLikeStatus(songId)
    }

    /**
     * 检查歌曲收藏状态
     * @param songId 歌曲ID
     * @return 是否已收藏
     */
    suspend fun checkLikeStatus(songId: Long): Boolean {
        return withContext(Dispatchers.IO) {
            musicDataSource.checkLikeStatus(songId)
        }
    }

    /**
     * 收藏歌曲
     * @param songId 歌曲ID
     * @return Flow<NetworkResult<Boolean>>
     */
    fun likeSongFlow(songId: Long): Flow<NetworkResult<Boolean>> = safeApiCall {
        musicDataSource.likeSong(songId)
    }

    /**
     * 收藏歌曲
     * @param songId 歌曲ID
     * @return 是否成功
     */
    suspend fun likeSong(songId: Long): Boolean {
        return withContext(Dispatchers.IO) {
            musicDataSource.likeSong(songId)
        }
    }

    /**
     * 取消收藏歌曲
     * @param songId 歌曲ID
     * @return Flow<NetworkResult<Boolean>>
     */
    fun unlikeSongFlow(songId: Long): Flow<NetworkResult<Boolean>> = safeApiCall {
        musicDataSource.unlikeSong(songId)
    }

    /**
     * 取消收藏歌曲
     * @param songId 歌曲ID
     * @return 是否成功
     */
    suspend fun unlikeSong(songId: Long): Boolean {
        return withContext(Dispatchers.IO) {
            musicDataSource.unlikeSong(songId)
        }
    }

    /**
     * 获取相似歌曲
     * @param songId 歌曲ID
     * @param limit 数量限制
     * @param forceRefresh 是否强制刷新
     * @return Flow<NetworkResult<List<Song>>>
     */
    fun getSimilarSongsFlow(songId: Long, limit: Int = 50, forceRefresh: Boolean = false): Flow<NetworkResult<List<Song>>> = cachedApiCall(
        cacheKey = "similar_songs_${songId}_$limit",
        forceRefresh = forceRefresh,
        apiCall = { musicDataSource.getSimilarSongs(songId, limit) }
    )

    /**
     * 获取相似歌曲
     * @param songId 歌曲ID
     * @param limit 数量限制
     * @param forceRefresh 是否强制刷新
     * @return List<Song>
     */
    suspend fun getSimilarSongs(songId: Long, limit: Int = 50, forceRefresh: Boolean = false): List<Song> {
        return withContext(Dispatchers.IO) {
            // 使用BaseRepository的缓存机制
            val cacheKey = "similar_songs_${songId}_$limit"
            val cachedData = getFromCache<List<Song>>(cacheKey)
            val currentTime = System.currentTimeMillis()

            if (!forceRefresh && cachedData != null && (currentTime - cachedData.second < CACHE_EXPIRATION_TIME)) {
                // 使用缓存数据
                return@withContext cachedData.first
            }

            // 从网络获取数据
            val songs = musicDataSource.getSimilarSongs(songId, limit)

            // 更新缓存
            putInCache(cacheKey, songs)

            songs
        }
    }

    /**
     * 获取相似歌曲并转换为MediaItem
     * @param songId 歌曲ID
     * @param limit 数量限制
     * @param forceRefresh 是否强制刷新
     * @return Flow<NetworkResult<List<MediaItem>>>
     */
    fun getSimilarSongsAsMediaItemsFlow(songId: Long, limit: Int = 50, forceRefresh: Boolean = false): Flow<NetworkResult<List<MediaItem>>> = flow {
        emit(NetworkResult.Loading)
        val songs = getSimilarSongs(songId, limit, forceRefresh)
        val mediaItems = songs.map { musicDataSource.songToMediaItem(it) }
        emit(NetworkResult.Success(mediaItems))
    }.flowOn(Dispatchers.IO)

    /**
     * 获取相似歌曲并转换为MediaItem
     * @param songId 歌曲ID
     * @param limit 数量限制
     * @param forceRefresh 是否强制刷新
     * @return 相似歌曲MediaItem列表
     */

    /**
     * 获取所有本地歌曲
     * @return Flow<NetworkResult<List<Song>>>
     */
    fun getLocalSongs(): Flow<NetworkResult<List<Song>>> = flow {
        emit(NetworkResult.Loading)
        musicDataSource.songDao.getAllLocalSongs().map { songEntities ->
            songEntities.map { entity ->
                // Convert SongEntity to Song
                Song(
                    id = entity.songId,
                    name = entity.title,
                    ar = entity.artist.split(",").map { Artist(0, it.trim()) },
                    al = Album(entity.albumId.toString(), entity.album, entity.albumCover),
                    dt = entity.duration,
                    // Add other necessary fields if available in SongEntity
                )
            }
        }.collect { songs ->
            emit(NetworkResult.Success(songs))
        }
    }.flowOn(Dispatchers.IO)

    /**
     * 获取收藏的歌曲
     * @return Flow<NetworkResult<List<Song>>>
     */
    fun getLikedSongs(): Flow<NetworkResult<List<Song>>> = flow {
        emit(NetworkResult.Loading)
        musicDataSource.getFavoriteSongs().map { songEntities ->
            songEntities.map { entity ->
                Song(
                    id = entity.songId,
                    name = entity.title,
                    ar = entity.artist.split(",").map { Artist(0, it.trim()) },
                    al = Album(entity.albumId.toString(), entity.album, entity.albumCover),
                    dt = entity.duration
                )
            }
        }.collect { songs ->
            emit(NetworkResult.Success(songs))
        }
    }.flowOn(Dispatchers.IO)

    /**
     * 获取用户创建的歌单
     * @return NetworkResult<List<PlayList>>
     */
    suspend fun getUserPlaylists(): NetworkResult<List<PlayList>> {
        // TODO: Implement actual API call and data conversion for user playlists
        // val response = musicDataSource.apiService.getUserPlaylists() // Assuming such an endpoint exists
        // return NetworkResult.Success(response.playlists ?: emptyList())
        return NetworkResult.Success(emptyList()) // Placeholder
    }

    /**
     * 获取用户收藏的歌单
     * @param uid 用户ID，如果为null则获取当前登录用户的歌单
     * @return NetworkResult<List<PlayList>>
     */
    suspend fun getCollectedPlaylists(uid: Long? = null): NetworkResult<List<PlayList>> {
        return try {
            // 使用缓存机制
            val cacheKey = "collected_playlists_${uid ?: "current"}"
            val cachedData = getFromCache<List<PlayList>>(cacheKey)
            val currentTime = System.currentTimeMillis()

            if (cachedData != null && (currentTime - cachedData.second < CACHE_EXPIRATION_TIME)) {
                // 使用缓存数据
                return NetworkResult.Success(cachedData.first)
            }

            // 调用API获取用户歌单
            val response = if (uid != null) {
                musicDataSource.apiService.getUserPlaylists(uid)
            } else {
                // 获取当前登录用户的歌单，需要先获取用户信息
                // 这里可以从SharedPreferences或其他地方获取当前用户ID
                // 暂时使用默认方式
                musicDataSource.apiService.getUserPlaylists()
            }

            val playlists = mutableListOf<PlayList>()

            // 解析API响应
            if (response.code == 200) {
                try {
                    val gson = com.google.gson.Gson()
                    val jsonString = gson.toJson(response)
                    val jsonObject = gson.fromJson(jsonString, com.google.gson.JsonObject::class.java)

                    // 获取playlist数组
                    if (jsonObject.has("playlist")) {
                        val playlistArray = jsonObject.getAsJsonArray("playlist")
                        for (playlistElement in playlistArray) {
                            val playlistObject = playlistElement.asJsonObject

                            // 解析歌单信息
                            val id = playlistObject.get("id")?.asString ?: ""
                            val name = playlistObject.get("name")?.asString ?: ""
                            val description = playlistObject.get("description")?.asString ?: ""
                            val coverImgUrl = playlistObject.get("coverImgUrl")?.asString ?: ""
                            val songCount = playlistObject.get("trackCount")?.asInt ?: 0
                            val playCount = playlistObject.get("playCount")?.asInt ?: 0
                            val subscribed = playlistObject.get("subscribed")?.asBoolean ?: false

                            // 解析创建者信息
                            var creatorId = ""
                            var creatorName = ""
                            if (playlistObject.has("creator")) {
                                val creatorObject = playlistObject.getAsJsonObject("creator")
                                creatorId = creatorObject.get("userId")?.asString ?: ""
                                creatorName = creatorObject.get("nickname")?.asString ?: ""
                            }

                            // 创建PlayList对象
                            val playlist = PlayList(
                                id = id,
                                name = name,
                                description = description,
                                coverImgUrl = coverImgUrl,
                                creatorId = creatorId,
                                creatorName = creatorName,
                                songCount = songCount,
                                playCount = playCount,
                                subscribed = subscribed
                            )

                            // 只添加收藏的歌单（非自己创建的）
                            if (subscribed) {
                                playlists.add(playlist)
                            }
                        }
                    }
                } catch (e: Exception) {
                    Log.e("MusicRepository", "解析用户歌单失败", e)
                }
            }

            // 更新缓存
            putInCache(cacheKey, playlists)

            NetworkResult.Success(playlists)
        } catch (e: Exception) {
            NetworkResult.Error(e.message ?: "获取收藏歌单失败")
        }
    }

    /**
     * 收藏/取消收藏歌单
     * @param playlistId 歌单ID
     * @param isSubscribe true为收藏，false为取消收藏
     * @return NetworkResult<Boolean>
     */
    suspend fun subscribePlaylist(playlistId: Long, isSubscribe: Boolean): NetworkResult<Boolean> {
        return try {
            // 调用收藏/取消收藏歌单API
            // 根据api.txt: /playlist/subscribe?t=1&id=106697785 (收藏)
            // /playlist/subscribe?t=2&id=106697785 (取消收藏)
            val t = if (isSubscribe) 1 else 2
            val response = musicDataSource.apiService.subscribePlaylist(t, playlistId)

            if (response.code == 200) {
                // 清除相关缓存
                clearPlaylistCache()
                NetworkResult.Success(true)
            } else {
                NetworkResult.Error("操作失败: ${response.message}")
            }
        } catch (e: Exception) {
            NetworkResult.Error(e.message ?: if (isSubscribe) "收藏歌单失败" else "取消收藏歌单失败")
        }
    }

    /**
     * 清除歌单相关缓存
     */
    private fun clearPlaylistCache() {
        try {
            // 清除收藏歌单缓存 - 使用BaseRepository提供的方法
            clearCache("collected_playlists_current")

            // 清除其他可能的歌单缓存
            for (i in 0..10) { // 假设最多有10个不同的用户ID缓存
                clearCache("collected_playlists_$i")
                clearCache("user_playlists_$i")
                clearCache("playlist_detail_$i")
            }
        } catch (e: Exception) {
            Log.e("MusicRepository", "清除歌单缓存失败", e)
        }
    }

    /**
     * 搜索歌曲 - 简化版本，统一接口
     * @param keyword 搜索关键词
     * @param limit 返回数量限制
     * @param offset 偏移量
     * @return Flow<NetworkResult<List<Song>>>
     */
    fun searchSongsFlow(keyword: String, limit: Int = 30, offset: Int = 0): Flow<NetworkResult<List<Song>>> = smartApiCall(
        apiType = ApiCallStrategy.API_TYPE_SEARCH,
        cacheKey = "search_songs_${keyword}_${limit}_$offset",
        primaryCall = { musicDataSource.searchSongs(keyword, limit, offset) },
        defaultValue = emptyList()
    )

    /**
     * 获取相似歌曲并转换为MediaItem
     * @param songId 歌曲ID
     * @param limit 数量限制
     * @param forceRefresh 是否强制刷新
     * @return 相似歌曲MediaItem列表
     */
    suspend fun getSimilarSongsAsMediaItems(songId: Long, limit: Int = 50, forceRefresh: Boolean = false): List<MediaItem> = withContext(Dispatchers.IO) {
        val songs = getSimilarSongs(songId, limit, forceRefresh)
        return@withContext songs.map { musicDataSource.songToMediaItem(it) }
    }

    /**
     * 搜索歌曲 - 统一方法
     * @param keywords 搜索关键词
     * @param limit 返回数量限制
     * @param offset 偏移量
     * @return 搜索结果歌曲列表
     */
    suspend fun searchSongs(keywords: String, limit: Int = 30, offset: Int = 0): List<Song> = withContext(Dispatchers.IO) {
        return@withContext musicDataSource.searchSongs(keywords, limit, offset)
    }

    /**
     * 获取搜索建议
     * @param keywords 搜索关键词
     * @return 搜索建议列表
     */
    suspend fun getSearchSuggestions(keywords: String): List<String> = withContext(Dispatchers.IO) {
        return@withContext musicDataSource.getSearchSuggestions(keywords)
    }

    /**
     * 搜索歌曲并转换为MediaItem
     * @param keywords 搜索关键词
     * @param limit 返回数量限制
     * @param offset 偏移量
     * @return 搜索结果MediaItem列表
     */
    suspend fun searchSongsAsMediaItems(keywords: String, limit: Int = 30, offset: Int = 0): List<MediaItem> = withContext(Dispatchers.IO) {
        val songs = musicDataSource.searchSongs(keywords, limit, offset)
        return@withContext songs.map { musicDataSource.songToMediaItem(it) }
    }

    /**
     * 清理过期缓存
     */
    fun clearExpiredCaches() {
        clearExpiredCache()
    }
}
