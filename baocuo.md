                                                                                                        2: In with_transaction.
                                                                                                        3: Out of keys.
                                                                                                        4: Error::Rc(ResponseCode(22))
2025-05-26 20:38:36.583  2991-3129  Finsky                  com.android.vending                  E  [219] okc.i(3): Failed to registerSync with Phenotype for experiment package com.google.android.finsky.stable. (Ask Gemini)
                                                                                                    java.util.concurrent.ExecutionException: com.google.android.libraries.phenotype.client.api.PhenotypeRuntimeException: 29504: 29504: Network error
                                                                                                    	at abkz.r(PG:21)
                                                                                                    	at abkz.get(PG:10)
                                                                                                    	at okc.h(PG:39)
                                                                                                    	at okc.doInBackground(PG:42)
                                                                                                    	at oke.d(PG:4)
                                                                                                    	at man.call(PG:997)
                                                                                                    	at abnn.a(PG:3)
                                                                                                    	at abmr.run(PG:21)
                                                                                                    	at abno.run(PG:5)
                                                                                                    	at abng.run(PG:76)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at dvo.run(PG:38)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
                                                                                                    Caused by: com.google.android.libraries.phenotype.client.api.PhenotypeRuntimeException: 29504: 29504: Network error
                                                                                                    	at xvr.call(PG:475)
                                                                                                    	at abnn.a(PG:3)
                                                                                                    	at abmr.run(PG:21)
                                                                                                    	at abno.run(PG:5)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637) 
                                                                                                    	at dvo.run(PG:38) 
                                                                                                    	at java.lang.Thread.run(Thread.java:1012) 
                                                                                                    Caused by: com.google.android.gms.phenotype.core.common.PhenotypeRuntimeException: 29504: Network error
                                                                                                    	at vsy.D(PG:230)
                                                                                                    	at vsy.n(PG:108)
                                                                                                    	at xvr.call(PG:386)
                                                                                                    	at abnn.a(PG:3) 
                                                                                                    	at abmr.run(PG:21) 
                                                                                                    	at abno.run(PG:5) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637) 
                                                                                                    	at dvo.run(PG:38) 
                                                                                                    	at java.lang.Thread.run(Thread.java:1012) 
                                                                                                    Caused by: org.apache.http.conn.ConnectTimeoutException: Connect to /142.250.217.74:443 timed out
                                                                                                    	at org.apache.http.conn.scheme.PlainSocketFactory.connectSocket(PlainSocketFactory.java:126)
                                                                                                    	at org.apache.http.impl.conn.DefaultClientConnectionOperator.openConnection(DefaultClientConnectionOperator.java:149)
                                                                                                    	at org.apache.http.impl.conn.AbstractPoolEntry.open(AbstractPoolEntry.java:170)
                                                                                                    	at org.apache.http.impl.conn.AbstractPooledConnAdapter.open(AbstractPooledConnAdapter.java:124)
                                                                                                    	at org.apache.http.impl.client.DefaultRequestDirector.execute(DefaultRequestDirector.java:366)
                                                                                                    	at org.apache.http.impl.client.AbstractHttpClient.execute(AbstractHttpClient.java:560)
                                                                                                    	at org.apache.http.impl.client.AbstractHttpClient.execute(AbstractHttpClient.java:492)
                                                                                                    	at org.apache.http.impl.client.AbstractHttpClient.execute(AbstractHttpClient.java:470)
                                                                                                    	at zba.execute(PG:5)
                                                                                                    	at abda.d(PG:7)
                                                                                                    	at zbe.b(PG:82)
                                                                                                    	at zbe.execute(PG:23)
                                                                                                    	at zbe.execute(PG:13)
                                                                                                    	at vtc.a(PG:230)
                                                                                                    	at vtw.g(PG:1)
                                                                                                    	at vtc.b(PG:1)
                                                                                                    	at vsy.D(PG:18)
                                                                                                    	at vsy.n(PG:108) 
                                                                                                    	at xvr.call(PG:386) 
                                                                                                    	at abnn.a(PG:3) 
                                                                                                    	at abmr.run(PG:21) 
                                                                                                    	at abno.run(PG:5) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637) 
                                                                                                    	at dvo.run(PG:38) 
                                                                                                    	at java.lang.Thread.run(Thread.java:1012) 
2025-05-26 20:38:36.596  2991-3082  Finsky                  com.android.vending                  E  [203] iuw.a(52): Unexpected android-id = 0
2025-05-26 20:38:36.624  2991-3082  Finsky                  com.android.vending                  E  [203] jhs.c(122): Unable to fetch checkin consistency token: empty token
2025-05-26 20:38:40.913  2708-2899  OpenGLRenderer          com.example.aimusicplayer            E  Unable to match the desired swap behavior.
2025-05-26 20:38:41.883   176-203   keystore2               keystore2                            E  keystore2::remote_provisioning: In get_remote_provisioning_key_and_certs: Error occurred: In get_rem_prov_attest_key: Failed to get a key
                                                                                                    
                                                                                                    Caused by:
                                                                                                        0: In get_rem_prov_attest_key_helper: Failed to assign a key
                                                                                                        1: In assign_attestation_key: 
                                                                                                        2: In with_transaction.
                                                                                                        3: Out of keys.
                                                                                                        4: Error::Rc(ResponseCode(22))
2025-05-26 20:38:42.267  3690-3737  Finsky                  com.android.vending                  E  [233] okc.i(3): Failed to registerSync with Phenotype for experiment package com.google.android.finsky.stable. (Ask Gemini)
                                                                                                    java.util.concurrent.ExecutionException: com.google.android.libraries.phenotype.client.api.PhenotypeRuntimeException: 29504: 29504: Network error
                                                                                                    	at abkz.r(PG:21)
                                                                                                    	at abkz.get(PG:10)
                                                                                                    	at okc.h(PG:39)
                                                                                                    	at okc.doInBackground(PG:42)
                                                                                                    	at oke.d(PG:4)
                                                                                                    	at man.call(PG:997)
                                                                                                    	at abnn.a(PG:3)
                                                                                                    	at abmr.run(PG:21)
                                                                                                    	at abno.run(PG:5)
                                                                                                    	at abng.run(PG:76)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at dvo.run(PG:38)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
                                                                                                    Caused by: com.google.android.libraries.phenotype.client.api.PhenotypeRuntimeException: 29504: 29504: Network error
                                                                                                    	at xvr.call(PG:475)
                                                                                                    	at abnn.a(PG:3)
                                                                                                    	at abmr.run(PG:21)
                                                                                                    	at abno.run(PG:5)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637) 
                                                                                                    	at dvo.run(PG:38) 
                                                                                                    	at java.lang.Thread.run(Thread.java:1012) 
                                                                                                    Caused by: com.google.android.gms.phenotype.core.common.PhenotypeRuntimeException: 29504: Network error
                                                                                                    	at vsy.D(PG:230)
                                                                                                    	at vsy.n(PG:108)
                                                                                                    	at xvr.call(PG:386)
                                                                                                    	at abnn.a(PG:3) 
                                                                                                    	at abmr.run(PG:21) 
                                                                                                    	at abno.run(PG:5) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637) 
                                                                                                    	at dvo.run(PG:38) 
                                                                                                    	at java.lang.Thread.run(Thread.java:1012) 
                                                                                                    Caused by: org.apache.http.conn.ConnectTimeoutException: Connect to /142.250.217.74:443 timed out
                                                                                                    	at org.apache.http.conn.scheme.PlainSocketFactory.connectSocket(PlainSocketFactory.java:126)
                                                                                                    	at org.apache.http.impl.conn.DefaultClientConnectionOperator.openConnection(DefaultClientConnectionOperator.java:149)
                                                                                                    	at org.apache.http.impl.conn.AbstractPoolEntry.open(AbstractPoolEntry.java:170)
                                                                                                    	at org.apache.http.impl.conn.AbstractPooledConnAdapter.open(AbstractPooledConnAdapter.java:124)
                                                                                                    	at org.apache.http.impl.client.DefaultRequestDirector.execute(DefaultRequestDirector.java:366)
                                                                                                    	at org.apache.http.impl.client.AbstractHttpClient.execute(AbstractHttpClient.java:560)
                                                                                                    	at org.apache.http.impl.client.AbstractHttpClient.execute(AbstractHttpClient.java:492)
                                                                                                    	at org.apache.http.impl.client.AbstractHttpClient.execute(AbstractHttpClient.java:470)
                                                                                                    	at zba.execute(PG:5)
                                                                                                    	at abda.d(PG:7)
                                                                                                    	at zbe.b(PG:82)
                                                                                                    	at zbe.execute(PG:23)
                                                                                                    	at zbe.execute(PG:13)
                                                                                                    	at vtc.a(PG:230)
                                                                                                    	at vtw.g(PG:1)
                                                                                                    	at vtc.b(PG:1)
                                                                                                    	at vsy.D(PG:18)
                                                                                                    	at vsy.n(PG:108) 
                                                                                                    	at xvr.call(PG:386) 
                                                                                                    	at abnn.a(PG:3) 
                                                                                                    	at abmr.run(PG:21) 
                                                                                                    	at abno.run(PG:5) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637) 
                                                                                                    	at dvo.run(PG:38) 
                                                                                                    	at java.lang.Thread.run(Thread.java:1012) 
2025-05-26 20:38:42.281  3690-5114  Finsky                  com.android.vending                  E  [324] iuw.a(52): Unexpected android-id = 0
2025-05-26 20:38:42.306  3690-5114  Finsky                  com.android.vending                  E  [324] jhs.c(122): Unable to fetch checkin consistency token: empty token
2025-05-26 20:38:43.827  2991-5665  FirebaseInstanceId      com.android.vending                  E  Failed to get FIS auth token (Ask Gemini)
                                                                                                    java.util.concurrent.ExecutionException: com.google.firebase.installations.FirebaseInstallationsException: Firebase Installations Service is unavailable. Please try again later.
                                                                                                    	at vtw.iJ(PG:32)
                                                                                                    	at vtw.G(PG:31)
                                                                                                    	at afgv.i(PG:183)
                                                                                                    	at acdh.a(PG:191)
                                                                                                    	at ujn.run(PG:251)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at vbl.run(PG:7)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
                                                                                                    Caused by: com.google.firebase.installations.FirebaseInstallationsException: Firebase Installations Service is unavailable. Please try again later.
                                                                                                    	at acei.b(PG:356)
                                                                                                    	at acdu.run(PG:233)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012) 
2025-05-26 20:38:43.894  2708-2899  OpenGLRenderer          com.example.aimusicplayer            E  Unable to match the desired swap behavior.
2025-05-26 20:38:49.167  1831-5743  bnan                    com.google.android.gms.persistent    E  Phenotype registration failed [CONTEXT service_id=51 ] (Ask Gemini)
                                                                                                    java.util.concurrent.TimeoutException: Timed out waiting for Task
                                                                                                    	at brml.n(:com.google.android.gms@242632114@24.26.32 (230800-650348549):53)
                                                                                                    	at bnan.a(:com.google.android.gms@242632114@24.26.32 (230800-650348549):68)
                                                                                                    	at com.google.android.gms.phenotype.sync.HeterodyneSyncTaskChimeraService.d(:com.google.android.gms@242632114@24.26.32 (230800-650348549):47)
                                                                                                    	at com.google.android.gms.phenotype.sync.HeterodyneSyncTaskChimeraService.a(:com.google.android.gms@242632114@24.26.32 (230800-650348549):126)
                                                                                                    	at avkn.call(:com.google.android.gms@242632114@24.26.32 (230800-650348549):32)
                                                                                                    	at java.util.concurrent.FutureTask.run(FutureTask.java:264)
                                                                                                    	at adtm.c(:com.google.android.gms@242632114@24.26.32 (230800-650348549):50)
                                                                                                    	at adtm.run(:com.google.android.gms@242632114@24.26.32 (230800-650348549):76)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at adys.run(:com.google.android.gms@242632114@24.26.32 (230800-650348549):8)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
2025-05-26 20:38:50.103  3690-5683  FirebaseInstanceId      com.android.vending                  E  Failed to get FIS auth token (Ask Gemini)
                                                                                                    java.util.concurrent.ExecutionException: com.google.firebase.installations.FirebaseInstallationsException: Firebase Installations Service is unavailable. Please try again later.
                                                                                                    	at vtw.iJ(PG:32)
                                                                                                    	at vtw.G(PG:31)
                                                                                                    	at afgv.i(PG:183)
                                                                                                    	at acdh.a(PG:191)
                                                                                                    	at ujn.run(PG:251)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at vbl.run(PG:7)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
                                                                                                    Caused by: com.google.firebase.installations.FirebaseInstallationsException: Firebase Installations Service is unavailable. Please try again later.
                                                                                                    	at acei.b(PG:356)
                                                                                                    	at acdu.run(PG:233)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012) 
--------- beginning of crash
2025-05-26 20:38:50.467  2708-5927  AndroidRuntime          com.example.aimusicplayer            E  FATAL EXCEPTION: OkHttp Dispatcher (Ask Gemini)
                                                                                                    Process: com.example.aimusicplayer, PID: 2708
                                                                                                    java.lang.IllegalStateException: closed
                                                                                                    	at okio.RealBufferedSource.request(RealBufferedSource.kt:206)
                                                                                                    	at okhttp3.logging.HttpLoggingInterceptor.intercept(HttpLoggingInterceptor.kt:247)
                                                                                                    	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
                                                                                                    	at okhttp3.internal.connection.RealCall.getResponseWithInterceptorChain$okhttp(RealCall.kt:201)
                                                                                                    	at okhttp3.internal.connection.RealCall$AsyncCall.run(RealCall.kt:517)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
2025-05-26 20:38:50.468  2708-2708  LoginViewModel          com.example.aimusicplayer            E  游客登录失败 (Ask Gemini)
                                                                                                    java.io.IOException: canceled due to java.lang.IllegalStateException: closed
                                                                                                    	at okhttp3.internal.connection.RealCall$AsyncCall.run(RealCall.kt:530)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
                                                                                                    	Suppressed: java.lang.IllegalStateException: closed
                                                                                                    		at okio.RealBufferedSource.request(RealBufferedSource.kt:206)
                                                                                                    		at okhttp3.logging.HttpLoggingInterceptor.intercept(HttpLoggingInterceptor.kt:247)
                                                                                                    		at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
                                                                                                    		at okhttp3.internal.connection.RealCall.getResponseWithInterceptorChain$okhttp(RealCall.kt:201)
                                                                                                    		at okhttp3.internal.connection.RealCall$AsyncCall.run(RealCall.kt:517)
                                                                                                    		... 3 more
2025-05-26 20:38:50.468  2708-2708  FlowViewModel           com.example.aimusicplayer            E  Error in ViewModel (Ask Gemini)
                                                                                                    java.io.IOException: canceled due to java.lang.IllegalStateException: closed
                                                                                                    	at okhttp3.internal.connection.RealCall$AsyncCall.run(RealCall.kt:530)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
                                                                                                    	Suppressed: java.lang.IllegalStateException: closed
                                                                                                    		at okio.RealBufferedSource.request(RealBufferedSource.kt:206)
                                                                                                    		at okhttp3.logging.HttpLoggingInterceptor.intercept(HttpLoggingInterceptor.kt:247)
                                                                                                    		at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
                                                                                                    		at okhttp3.internal.connection.RealCall.getResponseWithInterceptorChain$okhttp(RealCall.kt:201)
                                                                                                    		at okhttp3.internal.connection.RealCall$AsyncCall.run(RealCall.kt:517)
                                                                                                    		... 3 more
2025-05-26 20:38:50.480  2708-2708  GlobalErrorHandler      com.example.aimusicplayer            E  处理错误 (Ask Gemini)
                                                                                                    java.io.IOException: canceled due to java.lang.IllegalStateException: closed
                                                                                                    	at okhttp3.internal.connection.RealCall$AsyncCall.run(RealCall.kt:530)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
                                                                                                    	Suppressed: java.lang.IllegalStateException: closed
                                                                                                    		at okio.RealBufferedSource.request(RealBufferedSource.kt:206)
                                                                                                    		at okhttp3.logging.HttpLoggingInterceptor.intercept(HttpLoggingInterceptor.kt:247)
                                                                                                    		at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
                                                                                                    		at okhttp3.internal.connection.RealCall.getResponseWithInterceptorChain$okhttp(RealCall.kt:201)
                                                                                                    		at okhttp3.internal.connection.RealCall$AsyncCall.run(RealCall.kt:517)
                                                                                                    		... 3 more
2025-05-26 20:38:50.639  2165-2165  SurfaceSyncer           com.android.car.carlauncher          E  Failed to find sync for id=0
2025-05-26 20:38:50.639  2165-2165  SurfaceSyncer           com.android.car.carlauncher          E  Failed to find sync for id=1
2025-05-26 20:38:50.721  2165-2227  OpenGLRenderer          com.android.car.carlauncher          E  Unable to match the desired swap behavior.
2025-05-26 20:38:50.779  2165-2165  SurfaceSyncer           com.android.car.carlauncher          E  Failed to find sync for id=0
2025-05-26 20:38:50.780  2165-2165  SurfaceSyncer           com.android.car.carlauncher          E  Failed to find sync for id=1
2025-05-26 20:38:50.803   362-362   ClientCache             surfaceflinger                       E  failed to get buffer, invalid process token
2025-05-26 20:38:50.805   362-362   BpTransact...edListener surfaceflinger                       E  Failed to transact (-32)
2025-05-26 20:38:53.809   604-1791  TaskPersister           system_server                        E  File error accessing recents directory (directory doesn't exist?).
2025-05-26 20:38:53.809   604-1791  TaskPersister           system_server                        E  File error accessing recents directory (directory doesn't exist?).
2025-05-26 20:38:53.809   604-1791  TaskPersister           system_server                        E  File error accessing recents directory (directory doesn't exist?).
2025-05-26 20:38:57.657   604-1306  CellBroadcastUtils      system_server                        E  getDefaultCellBroadcastReceiverPackageName: no package found
2025-05-26 20:38:57.682   604-1306  CellBroadcastUtils      system_server                        E  getDefaultCellBroadcastReceiverPackageName: no package found
2025-05-26 20:38:57.844  2165-2227  OpenGLRenderer          com.android.car.carlauncher          E  Unable to match the desired swap behavior.
2025-05-26 20:38:57.889  2165-2227  OpenGLRenderer          com.android.car.carlauncher          E  Unable to match the desired swap behavior.
2025-05-26 20:38:59.877   604-2076  CellBroadcastUtils      system_server                        E  getDefaultCellBroadcastReceiverPackageName: no package found
2025-05-26 20:39:00.050  2165-2227  OpenGLRenderer          com.android.car.carlauncher          E  Unable to match the desired swap behavior.
2025-05-26 20:39:01.680  6032-6054  OpenGLRenderer          com.example.aimusicplayer            E  Unable to match the desired swap behavior.
2025-05-26 20:39:01.701   604-1791  TaskPersister           system_server                        E  File error accessing recents directory (directory doesn't exist?).
2025-05-26 20:39:01.701   604-1791  TaskPersister           system_server                        E  File error accessing recents directory (directory doesn't exist?).
2025-05-26 20:39:01.701   604-1791  TaskPersister           system_server                        E  File error accessing recents directory (directory doesn't exist?).
2025-05-26 20:39:02.844   604-2076  CellBroadcastUtils      system_server                        E  getDefaultCellBroadcastReceiverPackageName: no package found
2025-05-26 20:39:03.054  6032-6054  OpenGLRenderer          com.example.aimusicplayer            E  Unable to match the desired swap behavior.
2025-05-26 20:39:06.367   604-1791  TaskPersister           system_server                        E  File error accessing recents directory (directory doesn't exist?).
2025-05-26 20:39:06.367   604-1791  TaskPersister           system_server                        E  File error accessing recents directory (directory doesn't exist?).
2025-05-26 20:39:06.367   604-1791  TaskPersister           system_server                        E  File error accessing recents directory (directory doesn't exist?).
2025-05-26 20:39:09.087  6032-6078  AndroidRuntime          com.example.aimusicplayer            E  FATAL EXCEPTION: OkHttp Dispatcher (Ask Gemini)
                                                                                                    Process: com.example.aimusicplayer, PID: 6032
                                                                                                    java.lang.IllegalStateException: closed
                                                                                                    	at okio.RealBufferedSource.request(RealBufferedSource.kt:206)
                                                                                                    	at okhttp3.logging.HttpLoggingInterceptor.intercept(HttpLoggingInterceptor.kt:247)
                                                                                                    	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
                                                                                                    	at okhttp3.internal.connection.RealCall.getResponseWithInterceptorChain$okhttp(RealCall.kt:201)
                                                                                                    	at okhttp3.internal.connection.RealCall$AsyncCall.run(RealCall.kt:517)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
2025-05-26 20:39:09.088  6032-6032  LoginViewModel          com.example.aimusicplayer            E  游客登录失败 (Ask Gemini)
                                                                                                    java.io.IOException: canceled due to java.lang.IllegalStateException: closed
                                                                                                    	at okhttp3.internal.connection.RealCall$AsyncCall.run(RealCall.kt:530)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
                                                                                                    	Suppressed: java.lang.IllegalStateException: closed
                                                                                                    		at okio.RealBufferedSource.request(RealBufferedSource.kt:206)
                                                                                                    		at okhttp3.logging.HttpLoggingInterceptor.intercept(HttpLoggingInterceptor.kt:247)
                                                                                                    		at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
                                                                                                    		at okhttp3.internal.connection.RealCall.getResponseWithInterceptorChain$okhttp(RealCall.kt:201)
                                                                                                    		at okhttp3.internal.connection.RealCall$AsyncCall.run(RealCall.kt:517)
                                                                                                    		... 3 more
2025-05-26 20:39:09.088  6032-6032  FlowViewModel           com.example.aimusicplayer            E  Error in ViewModel (Ask Gemini)
                                                                                                    java.io.IOException: canceled due to java.lang.IllegalStateException: closed
                                                                                                    	at okhttp3.internal.connection.RealCall$AsyncCall.run(RealCall.kt:530)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
                                                                                                    	Suppressed: java.lang.IllegalStateException: closed
                                                                                                    		at okio.RealBufferedSource.request(RealBufferedSource.kt:206)
                                                                                                    		at okhttp3.logging.HttpLoggingInterceptor.intercept(HttpLoggingInterceptor.kt:247)
                                                                                                    		at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
                                                                                                    		at okhttp3.internal.connection.RealCall.getResponseWithInterceptorChain$okhttp(RealCall.kt:201)
                                                                                                    		at okhttp3.internal.connection.RealCall$AsyncCall.run(RealCall.kt:517)
                                                                                                    		... 3 more
2025-05-26 20:39:09.091  6032-6032  GlobalErrorHandler      com.example.aimusicplayer            E  处理错误 (Ask Gemini)
                                                                                                    java.io.IOException: canceled due to java.lang.IllegalStateException: closed
                                                                                                    	at okhttp3.internal.connection.RealCall$AsyncCall.run(RealCall.kt:530)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
                                                                                                    	Suppressed: java.lang.IllegalStateException: closed
                                                                                                    		at okio.RealBufferedSource.request(RealBufferedSource.kt:206)
                                                                                                    		at okhttp3.logging.HttpLoggingInterceptor.intercept(HttpLoggingInterceptor.kt:247)
                                                                                                    		at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
                                                                                                    		at okhttp3.internal.connection.RealCall.getResponseWithInterceptorChain$okhttp(RealCall.kt:201)
                                                                                                    		at okhttp3.internal.connection.RealCall$AsyncCall.run(RealCall.kt:517)
                                                                                                    		... 3 more
2025-05-26 20:39:09.232   604-1196  InputDispatcher         system_server                        E  But another display has a focused window
                                                                                                      FocusedWindows:
                                                                                                        displayId=6, name='b0aa193 com.android.systemui/com.android.systemui.car.distantdisplay.activity.RootTaskViewWallpaperActivity'
                                                                                                        displayId=5, name='4e7f6be com.android.systemui/com.android.systemui.car.distantdisplay.activity.NavigationTaskViewWallpaperActivity'
                                                                                                        displayId=3, name='aea37fd com.android.systemui/com.android.systemui.car.distantdisplay.activity.DistantDisplayActivity'
                                                                                                        displayId=2, name='d02c9d0 com.android.car.cluster.osdouble/com.android.car.cluster.osdouble.ClusterOsDoubleActivity'
2025-05-26 20:39:09.390  2165-2227  OpenGLRenderer          com.android.car.carlauncher          E  Unable to match the desired swap behavior.
2025-05-26 20:39:09.489   362-362   ClientCache             surfaceflinger                       E  failed to get buffer, invalid process token
2025-05-26 20:39:09.492   362-362   BpTransact...edListener surfaceflinger                       E  Failed to transact (-32)
2025-05-26 20:39:12.747   604-1791  TaskPersister           system_server                        E  File error accessing recents directory (directory doesn't exist?).
2025-05-26 20:39:12.747   604-1791  TaskPersister           system_server                        E  File error accessing recents directory (directory doesn't exist?).
2025-05-26 20:39:12.747   604-1791  TaskPersister           system_server                        E  File error accessing recents directory (directory doesn't exist?).
