
2025-05-26 19:25:54.625  3137-3336  Finsky                  com.android.vending                  E  [227] moe.a(110): ItemStore: getItems RPC failed.
2025-05-26 19:25:54.625  3137-3337  Finsky                  com.android.vending                  E  [228] moe.a(110): ItemStore: getItems RPC failed.
2025-05-26 19:25:54.625  3137-3337  Finsky                  com.android.vending                  E  [228] moe.a(110): ItemStore: getItems RPC failed.
2025-05-26 19:25:54.626  3137-3336  Finsky                  com.android.vending                  E  [227] moe.a(110): ItemStore: getItems RPC failed.
2025-05-26 19:25:54.626  3137-3337  Finsky                  com.android.vending                  E  [228] moe.a(110): ItemStore: getItems RPC failed.
2025-05-26 19:25:54.626  3137-3337  Finsky                  com.android.vending                  E  [228] moe.a(110): ItemStore: getItems RPC failed.
2025-05-26 19:25:54.626  3137-3337  Finsky                  com.android.vending                  E  [228] moe.a(110): ItemStore: getItems RPC failed.
2025-05-26 19:25:54.626  3137-3336  <PERSON>sky                  com.android.vending                  E  [227] moe.a(110): ItemStore: getItems RPC failed.
2025-05-26 19:25:54.626  3137-3337  Finsky                  com.android.vending                  E  [228] moe.a(110): ItemStore: getItems RPC failed.
2025-05-26 19:25:54.627  3137-3337  Finsky                  com.android.vending                  E  [228] moe.a(110): ItemStore: getItems RPC failed.
2025-05-26 19:25:54.627  3137-3336  Finsky                  com.android.vending                  E  [227] moe.a(110): ItemStore: getItems RPC failed.
2025-05-26 19:25:54.627  3137-3337  Finsky                  com.android.vending                  E  [228] moe.a(110): ItemStore: getItems RPC failed.
2025-05-26 19:25:54.627  3137-3336  Finsky                  com.android.vending                  E  [227] moe.a(110): ItemStore: getItems RPC failed.
2025-05-26 19:25:54.627  3137-3337  Finsky                  com.android.vending                  E  [228] moe.a(110): ItemStore: getItems RPC failed.
2025-05-26 19:25:54.627  3137-3336  Finsky                  com.android.vending                  E  [227] moe.a(110): ItemStore: getItems RPC failed.
2025-05-26 19:25:54.628  3137-3336  Finsky                  com.android.vending                  E  [227] moe.a(110): ItemStore: getItems RPC failed.
2025-05-26 19:25:54.628  3137-3336  Finsky                  com.android.vending                  E  [227] moe.a(110): ItemStore: getItems RPC failed.
2025-05-26 19:25:54.629  3137-3336  Finsky                  com.android.vending                  E  [227] moe.a(110): ItemStore: getItems RPC failed.
2025-05-26 19:25:54.629  3137-3336  Finsky                  com.android.vending                  E  [227] moe.a(110): ItemStore: getItems RPC failed.
2025-05-26 19:25:54.629  3137-3336  Finsky                  com.android.vending                  E  [227] moe.a(110): ItemStore: getItems RPC failed.
2025-05-26 19:25:54.630  3137-3336  Finsky                  com.android.vending                  E  [227] moe.a(110): ItemStore: getItems RPC failed.
2025-05-26 19:25:54.630  3137-3336  Finsky                  com.android.vending                  E  [227] moe.a(110): ItemStore: getItems RPC failed.
2025-05-26 19:25:54.631  3137-3336  Finsky                  com.android.vending                  E  [227] moe.a(110): ItemStore: getItems RPC failed.
2025-05-26 19:25:54.631  3137-3336  Finsky                  com.android.vending                  E  [227] moe.a(110): ItemStore: getItems RPC failed.
2025-05-26 19:25:54.631  3137-3336  Finsky                  com.android.vending                  E  [227] moe.a(110): ItemStore: getItems RPC failed.
2025-05-26 19:25:54.632  3137-3336  Finsky                  com.android.vending                  E  [227] moe.a(110): ItemStore: getItems RPC failed.
2025-05-26 19:25:54.632  3137-3336  Finsky                  com.android.vending                  E  [227] moe.a(110): ItemStore: getItems RPC failed.
2025-05-26 19:25:54.633  3137-3336  Finsky                  com.android.vending                  E  [227] moe.a(110): ItemStore: getItems RPC failed.
2025-05-26 19:25:54.633  3137-3311  Finsky                  com.android.vending                  E  [223] moe.a(110): ItemStore: getItems RPC failed.
2025-05-26 19:25:54.634  3137-3336  Finsky                  com.android.vending                  E  [227] moe.a(110): ItemStore: getItems RPC failed.
2025-05-26 19:25:54.634  3137-3311  Finsky                  com.android.vending                  E  [223] moe.a(110): ItemStore: getItems RPC failed.
2025-05-26 19:25:54.635  3137-3339  Finsky                  com.android.vending                  E  [230] moe.a(110): ItemStore: getItems RPC failed.
2025-05-26 19:25:54.635  3137-3339  Finsky                  com.android.vending                  E  [230] moe.a(110): ItemStore: getItems RPC failed.
2025-05-26 19:25:54.635  3137-3311  Finsky                  com.android.vending                  E  [223] moe.a(110): ItemStore: getItems RPC failed.
2025-05-26 19:25:54.635  3137-3339  Finsky                  com.android.vending                  E  [230] moe.a(110): ItemStore: getItems RPC failed.
2025-05-26 19:25:54.635  3137-3311  Finsky                  com.android.vending                  E  [223] moe.a(110): ItemStore: getItems RPC failed.
2025-05-26 19:25:54.636  3137-3336  Finsky                  com.android.vending                  E  [227] moe.a(110): ItemStore: getItems RPC failed.
2025-05-26 19:25:54.636  3137-3336  Finsky                  com.android.vending                  E  [227] moe.a(110): ItemStore: getItems RPC failed.
2025-05-26 19:25:54.636  3137-3336  Finsky                  com.android.vending                  E  [227] moe.a(110): ItemStore: getItems RPC failed.
2025-05-26 19:25:54.637  3137-3336  Finsky                  com.android.vending                  E  [227] moe.a(110): ItemStore: getItems RPC failed.
2025-05-26 19:25:54.637  3137-3336  Finsky                  com.android.vending                  E  [227] moe.a(110): ItemStore: getItems RPC failed.
2025-05-26 19:25:54.638  3137-3336  Finsky                  com.android.vending                  E  [227] moe.a(110): ItemStore: getItems RPC failed.
2025-05-26 19:25:54.638  3137-3336  Finsky                  com.android.vending                  E  [227] moe.a(110): ItemStore: getItems RPC failed.
2025-05-26 19:25:54.638  3137-3336  Finsky                  com.android.vending                  E  [227] moe.a(110): ItemStore: getItems RPC failed.
2025-05-26 19:25:54.638  3137-3311  Finsky                  com.android.vending                  E  [223] moe.a(110): ItemStore: getItems RPC failed.
2025-05-26 19:25:54.639  3137-3336  Finsky                  com.android.vending                  E  [227] moe.a(110): ItemStore: getItems RPC failed.
2025-05-26 19:25:54.639  3137-3336  Finsky                  com.android.vending                  E  [227] moe.a(110): ItemStore: getItems RPC failed.
2025-05-26 19:25:54.639  3137-3336  Finsky                  com.android.vending                  E  [227] moe.a(110): ItemStore: getItems RPC failed.
2025-05-26 19:25:54.639  3137-3336  Finsky                  com.android.vending                  E  [227] moe.a(110): ItemStore: getItems RPC failed.
2025-05-26 19:25:54.641  3137-3339  Finsky                  com.android.vending                  E  [230] moe.a(110): ItemStore: getItems RPC failed.
2025-05-26 19:25:54.642  3137-3339  Finsky                  com.android.vending                  E  [230] moe.a(110): ItemStore: getItems RPC failed.
2025-05-26 19:25:54.643  3137-3311  Finsky                  com.android.vending                  E  [223] moe.a(110): ItemStore: getItems RPC failed.
2025-05-26 19:25:54.643  3137-3339  Finsky                  com.android.vending                  E  [230] moe.a(110): ItemStore: getItems RPC failed.
2025-05-26 19:25:54.643  3137-3339  Finsky                  com.android.vending                  E  [230] moe.a(110): ItemStore: getItems RPC failed.
2025-05-26 19:25:54.643  3137-3339  Finsky                  com.android.vending                  E  [230] moe.a(110): ItemStore: getItems RPC failed.
2025-05-26 19:25:54.643  3137-3311  Finsky                  com.android.vending                  E  [223] moe.a(110): ItemStore: getItems RPC failed.
2025-05-26 19:25:54.643  3137-3339  Finsky                  com.android.vending                  E  [230] moe.a(110): ItemStore: getItems RPC failed.
2025-05-26 19:25:54.644  3137-3339  Finsky                  com.android.vending                  E  [230] moe.a(110): ItemStore: getItems RPC failed.
2025-05-26 19:25:54.644  3137-3336  Finsky                  com.android.vending                  E  [227] moe.a(110): ItemStore: getItems RPC failed.
2025-05-26 19:25:54.644  3137-3337  Finsky                  com.android.vending                  E  [228] moe.a(110): ItemStore: getItems RPC failed.
2025-05-26 19:25:54.644  3137-3339  Finsky                  com.android.vending                  E  [230] moe.a(110): ItemStore: getItems RPC failed.
2025-05-26 19:25:54.644  3137-3337  Finsky                  com.android.vending                  E  [228] moe.a(110): ItemStore: getItems RPC failed.
2025-05-26 19:25:54.644  3137-3337  Finsky                  com.android.vending                  E  [228] moe.a(110): ItemStore: getItems RPC failed.
2025-05-26 19:25:54.644  3137-3339  Finsky                  com.android.vending                  E  [230] moe.a(110): ItemStore: getItems RPC failed.
2025-05-26 19:25:54.644  3137-3339  Finsky                  com.android.vending                  E  [230] moe.a(110): ItemStore: getItems RPC failed.
2025-05-26 19:25:54.645  3137-3337  Finsky                  com.android.vending                  E  [228] moe.a(110): ItemStore: getItems RPC failed.
2025-05-26 19:25:54.645  3137-3339  Finsky                  com.android.vending                  E  [230] moe.a(110): ItemStore: getItems RPC failed.
2025-05-26 19:25:54.645  3137-3337  Finsky                  com.android.vending                  E  [228] moe.a(110): ItemStore: getItems RPC failed.
2025-05-26 19:25:54.645  3137-3339  Finsky                  com.android.vending                  E  [230] moe.a(110): ItemStore: getItems RPC failed.
2025-05-26 19:25:54.645  3137-3337  Finsky                  com.android.vending                  E  [228] moe.a(110): ItemStore: getItems RPC failed.
2025-05-26 19:25:54.645  3137-3336  Finsky                  com.android.vending                  E  [227] moe.a(110): ItemStore: getItems RPC failed.
2025-05-26 19:25:54.645  3137-3337  Finsky                  com.android.vending                  E  [228] moe.a(110): ItemStore: getItems RPC failed.
2025-05-26 19:25:54.645  3137-3336  Finsky                  com.android.vending                  E  [227] moe.a(110): ItemStore: getItems RPC failed.
2025-05-26 19:25:54.645  3137-3337  Finsky                  com.android.vending                  E  [228] moe.a(110): ItemStore: getItems RPC failed.
2025-05-26 19:25:54.646  3137-3337  Finsky                  com.android.vending                  E  [228] moe.a(110): ItemStore: getItems RPC failed.
2025-05-26 19:25:54.646  3137-3336  Finsky                  com.android.vending                  E  [227] moe.a(110): ItemStore: getItems RPC failed.
2025-05-26 19:25:54.646  3137-3337  Finsky                  com.android.vending                  E  [228] moe.a(110): ItemStore: getItems RPC failed.
2025-05-26 19:25:54.646  3137-3339  Finsky                  com.android.vending                  E  [230] moe.a(110): ItemStore: getItems RPC failed.
2025-05-26 19:25:58.014  3137-3311  Finsky                  com.android.vending                  E  [223] iuw.a(52): Unexpected android-id = 0
2025-05-26 19:25:58.014  3137-3311  Finsky                  com.android.vending                  E  [223] rcz.WA(261): [PLUS] Failed to trigger NOW_EXCLUSIVE urgency sync (Ask Gemini)
                                                                                                    java.lang.IllegalStateException: Cannot sync with 0 Android ID
                                                                                                    	at suw.a(PG:71)
                                                                                                    	at rak.a(PG:144)
                                                                                                    	at ablb.d(PG:3)
                                                                                                    	at abld.run(PG:42)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at kiq.run(PG:324)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
2025-05-26 19:25:58.015  3137-3311  Finsky                  com.android.vending                  E  [223] gmr.WA(29): [DeviceConfig] failed to update device attribute payloads (Ask Gemini)
                                                                                                    java.lang.IllegalStateException: Cannot sync with 0 Android ID
                                                                                                    	at suw.a(PG:71)
                                                                                                    	at rak.a(PG:144)
                                                                                                    	at ablb.d(PG:3)
                                                                                                    	at abld.run(PG:42)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at kiq.run(PG:324)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
2025-05-26 19:25:58.017  3137-3445  Finsky                  com.android.vending                  E  [256] kzd.run(1284): Upload device configuration failed
2025-05-26 19:25:58.454  1765-3984  WorkSourceUtil          com.google.android.gms               E  Could not find package: com.google.android.gms.westworld
2025-05-26 19:26:00.065  2750-2960  Finsky                  com.android.vending                  E  [223] iuw.a(52): Unexpected android-id = 0
2025-05-26 19:26:03.781   617-793   WifiScoringParams       system_server                        E  Invalid frequency(-1), using 5G as default rssi array
2025-05-26 19:26:07.949  1522-4873  WakeLock                com.google.android.gms.persistent    E  GCM_HB_ALARM release without a matched acquire!
2025-05-26 19:26:10.954  4899-4899  ng:quick_launch         com.android.vending                  E  Not starting debugger since process cannot load the jdwp agent.
2025-05-26 19:26:12.021  2750-2927  Finsky                  com.android.vending                  E  [212] iuw.a(52): Unexpected android-id = 0
2025-05-26 19:26:12.022  2750-2927  Finsky                  com.android.vending                  E  [212] rcz.WA(261): [PLUS] Failed to trigger NOW urgency sync (Ask Gemini)
                                                                                                    java.lang.IllegalStateException: Cannot sync with 0 Android ID
                                                                                                    	at suw.a(PG:71)
                                                                                                    	at rak.a(PG:144)
                                                                                                    	at ablb.d(PG:3)
                                                                                                    	at abld.run(PG:42)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at kiq.run(PG:324)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
2025-05-26 19:26:12.023  2750-2927  Finsky                  com.android.vending                  E  [212] obb.a(333): SCH: Job 37-48 threw exception (Ask Gemini)
                                                                                                    java.lang.IllegalStateException: Cannot sync with 0 Android ID
                                                                                                    	at suw.a(PG:71)
                                                                                                    	at rak.a(PG:144)
                                                                                                    	at ablb.d(PG:3)
                                                                                                    	at abld.run(PG:42)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at kiq.run(PG:324)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
2025-05-26 19:26:15.636  1522-4873  WakeLock                com.google.android.gms.persistent    E  GCM_HB_ALARM release without a matched acquire!
2025-05-26 19:26:22.116  1030-1030  SmsApplication          com.android.phone                    E  com.android.mms.service does not have system signature
2025-05-26 19:26:22.174  1030-1030  SmsApplication          com.android.phone                    E  com.android.mms.service does not have system signature
2025-05-26 19:26:22.183  3137-3389  Finsky                  com.android.vending                  E  [247] iuw.a(52): Unexpected android-id = 0
2025-05-26 19:26:22.234  1030-1030  SmsApplication          com.android.phone                    E  com.android.mms.service does not have system signature
2025-05-26 19:26:22.235   441-451   installd                installd                             E  Couldn't opendir /data/app/vmdl2071452384.tmp: No such file or directory
2025-05-26 19:26:22.235   441-451   installd                installd                             E  Failed to delete /data/app/vmdl2071452384.tmp: No such file or directory
2025-05-26 19:26:22.542  2055-2055  ChimeraRcvrProxy        com.google.android.gms               E  com.google.android.gms.gass.chimera.PackageChangeBroadcastReceiver dropping broadcast android.intent.action.PACKAGE_ADDED
2025-05-26 19:26:22.823  2055-2055  ChimeraRcvrProxy        com.google.android.gms               E  com.google.android.gms.gass.chimera.PackageChangeBroadcastReceiver dropping broadcast android.intent.action.PACKAGE_REPLACED
2025-05-26 19:26:23.188   617-716   CellBroadcastUtils      system_server                        E  getDefaultCellBroadcastReceiverPackageName: no package found
2025-05-26 19:26:23.610  2232-2385  OpenGLRenderer          com.android.car.carlauncher          E  Unable to match the desired swap behavior.
2025-05-26 19:26:23.743  2055-4822  ProtoLiteUtils          com.google.android.gms               E  Corrupt protobuf data, expected CRC: 82 computed CRC: 0
2025-05-26 19:26:24.157  2055-4822  ProtoLiteUtils          com.google.android.gms               E  Corrupt protobuf data, expected CRC: 82 computed CRC: 0
2025-05-26 19:26:25.070  2055-2181  ProtoLiteUtils          com.google.android.gms               E  Corrupt protobuf data, expected CRC: 82 computed CRC: 0
2025-05-26 19:26:25.251  2055-2181  ProtoLiteUtils          com.google.android.gms               E  Corrupt protobuf data, expected CRC: 82 computed CRC: 0
2025-05-26 19:26:25.872  3137-3339  Finsky                  com.android.vending                  E  [230] iuw.a(52): Unexpected android-id = 0
2025-05-26 19:26:25.872  3137-3339  Finsky                  com.android.vending                  E  [230] rcz.WA(261): [PLUS] Failed to trigger NOW_EXCLUSIVE urgency sync (Ask Gemini)
                                                                                                    java.lang.IllegalStateException: Cannot sync with 0 Android ID
                                                                                                    	at suw.a(PG:71)
                                                                                                    	at rak.a(PG:144)
                                                                                                    	at ablb.d(PG:3)
                                                                                                    	at abld.run(PG:42)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at kiq.run(PG:324)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
2025-05-26 19:26:25.872  3137-3339  Finsky                  com.android.vending                  E  [230] gmr.WA(29): [DeviceConfig] failed to update device attribute payloads (Ask Gemini)
                                                                                                    java.lang.IllegalStateException: Cannot sync with 0 Android ID
                                                                                                    	at suw.a(PG:71)
                                                                                                    	at rak.a(PG:144)
                                                                                                    	at ablb.d(PG:3)
                                                                                                    	at abld.run(PG:42)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at kiq.run(PG:324)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
2025-05-26 19:26:25.898  3137-3388  Finsky                  com.android.vending                  E  [246] iuw.a(52): Unexpected android-id = 0
2025-05-26 19:26:26.359  5099-5135  OpenGLRenderer          com.example.aimusicplayer            E  Unable to match the desired swap behavior.
2025-05-26 19:26:26.922   617-2139  TaskPersister           system_server                        E  File error accessing recents directory (directory doesn't exist?).
2025-05-26 19:26:26.922   617-2139  TaskPersister           system_server                        E  File error accessing recents directory (directory doesn't exist?).
2025-05-26 19:26:26.922   617-2139  TaskPersister           system_server                        E  File error accessing recents directory (directory doesn't exist?).
2025-05-26 19:26:27.468   617-3119  CellBroadcastUtils      system_server                        E  getDefaultCellBroadcastReceiverPackageName: no package found
2025-05-26 19:26:27.882  5099-5135  OpenGLRenderer          com.example.aimusicplayer            E  Unable to match the desired swap behavior.
2025-05-26 19:26:29.546  5099-5135  OpenGLRenderer          com.example.aimusicplayer            E  Unable to match the desired swap behavior.
2025-05-26 19:26:30.623  3137-3389  Finsky                  com.android.vending                  E  [247] iuw.a(52): Unexpected android-id = 0
2025-05-26 19:26:31.023   617-2139  TaskPersister           system_server                        E  File error accessing recents directory (directory doesn't exist?).
2025-05-26 19:26:31.023   617-2139  TaskPersister           system_server                        E  File error accessing recents directory (directory doesn't exist?).
2025-05-26 19:26:31.023   617-2139  TaskPersister           system_server                        E  File error accessing recents directory (directory doesn't exist?).
2025-05-26 19:26:34.322  3137-3388  Finsky                  com.android.vending                  E  [246] iuw.a(52): Unexpected android-id = 0
2025-05-26 19:26:37.202  1765-2376  WakeLock                com.google.android.gms               E  IntentOp:.chimera.container.InitConfigOperation ** IS FORCE-RELEASED ON TIMEOUT **
2025-05-26 19:26:41.285  3137-3311  Finsky                  com.android.vending                  E  [223] moe.a(110): ItemStore: getItems RPC failed.
2025-05-26 19:26:41.285  3137-3339  Finsky                  com.android.vending                  E  [230] moe.a(110): ItemStore: getItems RPC failed.
2025-05-26 19:26:45.373  5261-5261  ng:quick_launch         com.android.vending                  E  Not starting debugger since process cannot load the jdwp agent.
2025-05-26 19:26:45.945  3137-3336  Finsky                  com.android.vending                  E  [227] iuw.a(52): Unexpected android-id = 0
2025-05-26 19:26:45.945  3137-3336  Finsky                  com.android.vending                  E  [227] rcz.WA(261): [PLUS] Failed to trigger NOW urgency sync (Ask Gemini)
                                                                                                    java.lang.IllegalStateException: Cannot sync with 0 Android ID
                                                                                                    	at suw.a(PG:71)
                                                                                                    	at rak.a(PG:144)
                                                                                                    	at ablb.d(PG:3)
                                                                                                    	at abld.run(PG:42)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at kiq.run(PG:324)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
2025-05-26 19:26:45.946  3137-3336  Finsky                  com.android.vending                  E  [227] obb.a(333): SCH: Job 37-46 threw exception (Ask Gemini)
                                                                                                    java.lang.IllegalStateException: Cannot sync with 0 Android ID
                                                                                                    	at suw.a(PG:71)
                                                                                                    	at rak.a(PG:144)
                                                                                                    	at ablb.d(PG:3)
                                                                                                    	at abld.run(PG:42)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at kiq.run(PG:324)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
2025-05-26 19:26:51.859  2055-2319  WakeLock                com.google.android.gms               E  IntentOp:.chimera.container.InitConfigOperation ** IS FORCE-RELEASED ON TIMEOUT **
2025-05-26 19:26:56.263  5099-5135  OpenGLRenderer          com.example.aimusicplayer            E  Unable to match the desired swap behavior.
2025-05-26 19:26:57.988  5099-5135  OpenGLRenderer          com.example.aimusicplayer            E  Unable to match the desired swap behavior.
2025-05-26 19:26:59.945  1950-3125  OpenGLRenderer          com...d.apps.automotive.inputmethod  E  Unable to match the desired swap behavior.
2025-05-26 19:27:02.338   347-347   android.ha...rvice-mock <EMAIL>  E  Failed to getEnergyData
2025-05-26 19:27:03.046  1522-5319  WakeLock                com.google.android.gms.persistent    E  GCM_HB_ALARM release without a matched acquire!
2025-05-26 19:27:06.710  5099-5135  OpenGLRenderer          com.example.aimusicplayer            E  Unable to match the desired swap behavior.
2025-05-26 19:27:06.938  5099-5135  OpenGLRenderer          com.example.aimusicplayer            E  Unable to match the desired swap behavior.
2025-05-26 19:27:13.695  1522-5382  WakeLock                com.google.android.gms.persistent    E  GCM_HB_ALARM release without a matched acquire!
2025-05-26 19:27:17.778  5099-5135  OpenGLRenderer          com.example.aimusicplayer            E  Unable to match the desired swap behavior.
2025-05-26 19:27:18.017  5099-5135  OpenGLRenderer          com.example.aimusicplayer            E  Unable to match the desired swap behavior.
2025-05-26 19:27:18.301  5099-5099  LoginViewModel          com.example.aimusicplayer            E  验证码登录失败 (Ask Gemini)
                                                                                                    retrofit2.HttpException: HTTP 400 
                                                                                                    	at retrofit2.KotlinExtensions$await$2$2.onResponse(KotlinExtensions.kt:53)
                                                                                                    	at retrofit2.OkHttpCall$1.onResponse(OkHttpCall.java:161)
                                                                                                    	at okhttp3.internal.connection.RealCall$AsyncCall.run(RealCall.kt:519)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
2025-05-26 19:27:18.302  5099-5099  FlowViewModel           com.example.aimusicplayer            E  Error in ViewModel (Ask Gemini)
                                                                                                    retrofit2.HttpException: HTTP 400 
                                                                                                    	at retrofit2.KotlinExtensions$await$2$2.onResponse(KotlinExtensions.kt:53)
                                                                                                    	at retrofit2.OkHttpCall$1.onResponse(OkHttpCall.java:161)
                                                                                                    	at okhttp3.internal.connection.RealCall$AsyncCall.run(RealCall.kt:519)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
2025-05-26 19:27:18.303  5099-5099  GlobalErrorHandler      com.example.aimusicplayer            E  处理错误 (Ask Gemini)
                                                                                                    retrofit2.HttpException: HTTP 400 
                                                                                                    	at retrofit2.KotlinExtensions$await$2$2.onResponse(KotlinExtensions.kt:53)
                                                                                                    	at retrofit2.OkHttpCall$1.onResponse(OkHttpCall.java:161)
                                                                                                    	at okhttp3.internal.connection.RealCall$AsyncCall.run(RealCall.kt:519)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
2025-05-26 19:27:18.308  5099-5099  LoginActivity           com.example.aimusicplayer            E  错误信息: 客户端错误 (400)，请稍后重试
2025-05-26 19:27:18.391  5099-5135  OpenGLRenderer          com.example.aimusicplayer            E  Unable to match the desired swap behavior.
2025-05-26 19:27:21.237  1073-1235  OpenGLRenderer          com.android.systemui                 E  Unable to match the desired swap behavior.
2025-05-26 19:27:23.791   617-793   WifiScoringParams       system_server                        E  Invalid frequency(-1), using 5G as default rssi array
2025-05-26 19:27:23.956  2750-2942  Finsky                  com.android.vending                  E  [216] okc.i(3): Failed to registerSync with Phenotype for experiment package com.google.android.finsky.regular. (Ask Gemini)
                                                                                                    java.util.concurrent.ExecutionException: com.google.android.libraries.phenotype.client.api.PhenotypeRuntimeException: 29504: 29504: Network error
                                                                                                    	at abkz.r(PG:21)
                                                                                                    	at abkz.get(PG:10)
                                                                                                    	at okc.h(PG:39)
                                                                                                    	at okc.doInBackground(PG:42)
                                                                                                    	at oke.d(PG:4)
                                                                                                    	at man.call(PG:997)
                                                                                                    	at abnn.a(PG:3)
                                                                                                    	at abmr.run(PG:21)
                                                                                                    	at abno.run(PG:5)
                                                                                                    	at abnf.run(PG:3)
                                                                                                    	at abng.run(PG:76)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at dvo.run(PG:38)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
                                                                                                    Caused by: com.google.android.libraries.phenotype.client.api.PhenotypeRuntimeException: 29504: 29504: Network error
                                                                                                    	at xvr.call(PG:475)
                                                                                                    	at abnn.a(PG:3)
                                                                                                    	at abmr.run(PG:21)
                                                                                                    	at abno.run(PG:5)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637) 
                                                                                                    	at dvo.run(PG:38) 
                                                                                                    	at java.lang.Thread.run(Thread.java:1012) 
                                                                                                    Caused by: com.google.android.gms.phenotype.core.common.PhenotypeRuntimeException: 29504: Network error
                                                                                                    	at vsy.D(PG:230)
                                                                                                    	at vsy.n(PG:108)
                                                                                                    	at xvr.call(PG:386)
                                                                                                    	at abnn.a(PG:3) 
                                                                                                    	at abmr.run(PG:21) 
                                                                                                    	at abno.run(PG:5) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637) 
                                                                                                    	at dvo.run(PG:38) 
                                                                                                    	at java.lang.Thread.run(Thread.java:1012) 
                                                                                                    Caused by: org.apache.http.conn.ConnectTimeoutException: Connect to /142.250.217.106:443 timed out
                                                                                                    	at org.apache.http.conn.scheme.PlainSocketFactory.connectSocket(PlainSocketFactory.java:126)
                                                                                                    	at org.apache.http.impl.conn.DefaultClientConnectionOperator.openConnection(DefaultClientConnectionOperator.java:149)
                                                                                                    	at org.apache.http.impl.conn.AbstractPoolEntry.open(AbstractPoolEntry.java:170)
                                                                                                    	at org.apache.http.impl.conn.AbstractPooledConnAdapter.open(AbstractPooledConnAdapter.java:124)
                                                                                                    	at org.apache.http.impl.client.DefaultRequestDirector.execute(DefaultRequestDirector.java:366)
                                                                                                    	at org.apache.http.impl.client.AbstractHttpClient.execute(AbstractHttpClient.java:560)
                                                                                                    	at org.apache.http.impl.client.AbstractHttpClient.execute(AbstractHttpClient.java:492)
                                                                                                    	at org.apache.http.impl.client.AbstractHttpClient.execute(AbstractHttpClient.java:470)
                                                                                                    	at zba.execute(PG:5)
                                                                                                    	at abda.d(PG:7)
                                                                                                    	at zbe.b(PG:82)
                                                                                                    	at zbe.execute(PG:23)
                                                                                                    	at zbe.execute(PG:13)
                                                                                                    	at vtc.a(PG:230)
                                                                                                    	at vtw.g(PG:1)
                                                                                                    	at vtc.b(PG:1)
                                                                                                    	at vsy.D(PG:18)
                                                                                                    	at vsy.n(PG:108) 
                                                                                                    	at xvr.call(PG:386) 
                                                                                                    	at abnn.a(PG:3) 
                                                                                                    	at abmr.run(PG:21) 
                                                                                                    	at abno.run(PG:5) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637) 
                                                                                                    	at dvo.run(PG:38) 
                                                                                                    	at java.lang.Thread.run(Thread.java:1012) 
2025-05-26 19:27:23.973  2750-2862  Finsky                  com.android.vending                  E  [200] iuw.a(52): Unexpected android-id = 0
2025-05-26 19:27:23.986  2750-2862  Finsky                  com.android.vending                  E  [200] jhs.c(122): Unable to fetch checkin consistency token: empty token
2025-05-26 19:27:27.369  5099-5099  LoginViewModel          com.example.aimusicplayer            E  游客登录失败 (Ask Gemini)
                                                                                                    java.io.IOException: canceled due to java.lang.IllegalStateException: closed
                                                                                                    	at okhttp3.internal.connection.RealCall$AsyncCall.run(RealCall.kt:530)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
                                                                                                    	Suppressed: java.lang.IllegalStateException: closed
                                                                                                    		at okio.RealBufferedSource.request(RealBufferedSource.kt:206)
                                                                                                    		at okhttp3.logging.HttpLoggingInterceptor.intercept(HttpLoggingInterceptor.kt:247)
                                                                                                    		at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
                                                                                                    		at okhttp3.internal.connection.RealCall.getResponseWithInterceptorChain$okhttp(RealCall.kt:201)
                                                                                                    		at okhttp3.internal.connection.RealCall$AsyncCall.run(RealCall.kt:517)
                                                                                                    		... 3 more
2025-05-26 19:27:27.369  5099-5180  AndroidRuntime          com.example.aimusicplayer            E  FATAL EXCEPTION: OkHttp Dispatcher (Ask Gemini)
                                                                                                    Process: com.example.aimusicplayer, PID: 5099
                                                                                                    java.lang.IllegalStateException: closed
                                                                                                    	at okio.RealBufferedSource.request(RealBufferedSource.kt:206)
                                                                                                    	at okhttp3.logging.HttpLoggingInterceptor.intercept(HttpLoggingInterceptor.kt:247)
                                                                                                    	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
                                                                                                    	at okhttp3.internal.connection.RealCall.getResponseWithInterceptorChain$okhttp(RealCall.kt:201)
                                                                                                    	at okhttp3.internal.connection.RealCall$AsyncCall.run(RealCall.kt:517)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
2025-05-26 19:27:27.369  5099-5099  FlowViewModel           com.example.aimusicplayer            E  Error in ViewModel (Ask Gemini)
                                                                                                    java.io.IOException: canceled due to java.lang.IllegalStateException: closed
                                                                                                    	at okhttp3.internal.connection.RealCall$AsyncCall.run(RealCall.kt:530)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
                                                                                                    	Suppressed: java.lang.IllegalStateException: closed
                                                                                                    		at okio.RealBufferedSource.request(RealBufferedSource.kt:206)
                                                                                                    		at okhttp3.logging.HttpLoggingInterceptor.intercept(HttpLoggingInterceptor.kt:247)
                                                                                                    		at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
                                                                                                    		at okhttp3.internal.connection.RealCall.getResponseWithInterceptorChain$okhttp(RealCall.kt:201)
                                                                                                    		at okhttp3.internal.connection.RealCall$AsyncCall.run(RealCall.kt:517)
                                                                                                    		... 3 more
2025-05-26 19:27:27.370  5099-5099  GlobalErrorHandler      com.example.aimusicplayer            E  处理错误 (Ask Gemini)
                                                                                                    java.io.IOException: canceled due to java.lang.IllegalStateException: closed
                                                                                                    	at okhttp3.internal.connection.RealCall$AsyncCall.run(RealCall.kt:530)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
                                                                                                    	Suppressed: java.lang.IllegalStateException: closed
                                                                                                    		at okio.RealBufferedSource.request(RealBufferedSource.kt:206)
                                                                                                    		at okhttp3.logging.HttpLoggingInterceptor.intercept(HttpLoggingInterceptor.kt:247)
                                                                                                    		at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
                                                                                                    		at okhttp3.internal.connection.RealCall.getResponseWithInterceptorChain$okhttp(RealCall.kt:201)
                                                                                                    		at okhttp3.internal.connection.RealCall$AsyncCall.run(RealCall.kt:517)
                                                                                                    		... 3 more
2025-05-26 19:27:27.373  5099-5099  LoginActivity           com.example.aimusicplayer            E  错误信息: 网络错误，请检查网络连接后重试
2025-05-26 19:27:27.498  2232-2385  OpenGLRenderer          com.android.car.carlauncher          E  Unable to match the desired swap behavior.
2025-05-26 19:27:27.510  2232-2232  SurfaceSyncer           com.android.car.carlauncher          E  Failed to find sync for id=0
2025-05-26 19:27:27.511  2232-2232  SurfaceSyncer           com.android.car.carlauncher          E  Failed to find sync for id=1
2025-05-26 19:27:27.542   361-361   ClientCache             surfaceflinger                       E  failed to get buffer, invalid process token
2025-05-26 19:27:27.544   361-361   BpTransact...edListener surfaceflinger                       E  Failed to transact (-32)
2025-05-26 19:27:27.546  2232-2232  SurfaceSyncer           com.android.car.carlauncher          E  Failed to find sync for id=0
2025-05-26 19:27:27.546  2232-2232  SurfaceSyncer           com.android.car.carlauncher          E  Failed to find sync for id=1
2025-05-26 19:27:30.549   617-2139  TaskPersister           system_server                        E  File error accessing recents directory (directory doesn't exist?).
2025-05-26 19:27:30.549   617-2139  TaskPersister           system_server                        E  File error accessing recents directory (directory doesn't exist?).
2025-05-26 19:27:30.549   617-2139  TaskPersister           system_server                        E  File error accessing recents directory (directory doesn't exist?).
2025-05-26 19:27:34.411  3137-3269  Finsky                  com.android.vending                  E  [215] okc.i(3): Failed to registerSync with Phenotype for experiment package com.google.android.finsky.stable. (Ask Gemini)
                                                                                                    java.util.concurrent.ExecutionException: com.google.android.libraries.phenotype.client.api.PhenotypeRuntimeException: 29504: 29504: Network error
                                                                                                    	at abkz.r(PG:21)
                                                                                                    	at abkz.get(PG:10)
                                                                                                    	at okc.h(PG:39)
                                                                                                    	at okc.doInBackground(PG:42)
                                                                                                    	at oke.d(PG:4)
                                                                                                    	at man.call(PG:997)
                                                                                                    	at abnn.a(PG:3)
                                                                                                    	at abmr.run(PG:21)
                                                                                                    	at abno.run(PG:5)
                                                                                                    	at abnf.run(PG:3)
                                                                                                    	at abng.run(PG:76)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at dvo.run(PG:38)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
                                                                                                    Caused by: com.google.android.libraries.phenotype.client.api.PhenotypeRuntimeException: 29504: 29504: Network error
                                                                                                    	at xvr.call(PG:475)
                                                                                                    	at abnn.a(PG:3)
                                                                                                    	at abmr.run(PG:21)
                                                                                                    	at abno.run(PG:5)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637) 
                                                                                                    	at dvo.run(PG:38) 
                                                                                                    	at java.lang.Thread.run(Thread.java:1012) 
                                                                                                    Caused by: com.google.android.gms.phenotype.core.common.PhenotypeRuntimeException: 29504: Network error
                                                                                                    	at vsy.D(PG:230)
                                                                                                    	at vsy.n(PG:108)
                                                                                                    	at xvr.call(PG:386)
                                                                                                    	at abnn.a(PG:3) 
                                                                                                    	at abmr.run(PG:21) 
                                                                                                    	at abno.run(PG:5) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637) 
                                                                                                    	at dvo.run(PG:38) 
                                                                                                    	at java.lang.Thread.run(Thread.java:1012) 
                                                                                                    Caused by: org.apache.http.conn.ConnectTimeoutException: Connect to /142.250.217.106:443 timed out
                                                                                                    	at org.apache.http.conn.scheme.PlainSocketFactory.connectSocket(PlainSocketFactory.java:126)
                                                                                                    	at org.apache.http.impl.conn.DefaultClientConnectionOperator.openConnection(DefaultClientConnectionOperator.java:149)
                                                                                                    	at org.apache.http.impl.conn.AbstractPoolEntry.open(AbstractPoolEntry.java:170)
                                                                                                    	at org.apache.http.impl.conn.AbstractPooledConnAdapter.open(AbstractPooledConnAdapter.java:124)
                                                                                                    	at org.apache.http.impl.client.DefaultRequestDirector.execute(DefaultRequestDirector.java:366)
                                                                                                    	at org.apache.http.impl.client.AbstractHttpClient.execute(AbstractHttpClient.java:560)
                                                                                                    	at org.apache.http.impl.client.AbstractHttpClient.execute(AbstractHttpClient.java:492)
                                                                                                    	at org.apache.http.impl.client.AbstractHttpClient.execute(AbstractHttpClient.java:470)
                                                                                                    	at zba.execute(PG:5)
                                                                                                    	at abda.d(PG:7)
                                                                                                    	at zbe.b(PG:82)
                                                                                                    	at zbe.execute(PG:23)
                                                                                                    	at zbe.execute(PG:13)
                                                                                                    	at vtc.a(PG:230)
                                                                                                    	at vtw.g(PG:1)
                                                                                                    	at vtc.b(PG:1)
                                                                                                    	at vsy.D(PG:18)
                                                                                                    	at vsy.n(PG:108) 
                                                                                                    	at xvr.call(PG:386) 
                                                                                                    	at abnn.a(PG:3) 
                                                                                                    	at abmr.run(PG:21) 
                                                                                                    	at abno.run(PG:5) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637) 
                                                                                                    	at dvo.run(PG:38) 
                                                                                                    	at java.lang.Thread.run(Thread.java:1012) 
2