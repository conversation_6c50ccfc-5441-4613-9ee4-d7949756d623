{"timestamp": "2025-05-26T12:04:32.547Z", "summary": {"totalFlows": 6, "passedFlows": 4, "warningFlows": 2, "failedFlows": 0, "successRate": 100}, "detailed_results": {"primary": {"qrLogin": {"name": "二维码登录流程", "totalSteps": 3, "passedSteps": 3, "warningSteps": 0, "failedSteps": 0, "results": [{"passed": true, "result": {"success": true, "statusCode": 200, "headers": {"server": "openresty", "date": "Mon, 26 May 2025 12:04:23 GMT", "content-type": "application/json; charset=utf-8", "content-length": "80", "connection": "keep-alive", "x-powered-by": "Express", "access-control-allow-credentials": "true", "access-control-allow-origin": "https://ncm.zhenxin.me", "access-control-allow-headers": "X-Requested-With,Content-Type", "access-control-allow-methods": "PUT,POST,GET,DELETE,OPTIONS", "set-cookie": ["NMTID=00OGKgpueRViuv5f03el9Ww3erJRwwAAAGXDHrL2Q; Max-Age=********0; Expires=Thu, 24 May 2035 12:04:23 GMT; Path=/;; SameSite=None; Secure"], "etag": "W/\"50-joxTqUkjxSAA6guM//opnM0ZeR8\"", "cache-control": "max-age=120", "strict-transport-security": "max-age=********", "alt-svc": "h3=\":443\"; ma=2592000"}, "data": {"data": {"code": 200, "unikey": "99c1f925-fce6-4451-a3e6-2b353327a936"}, "code": 200}, "cookies": ["NMTID=00OGKgpueRViuv5f03el9Ww3erJRwwAAAGXDHrL2Q; Max-Age=********0; Expires=Thu, 24 May 2035 12:04:23 GMT; Path=/;; SameSite=None; Secure"]}}, {"passed": true, "result": {"success": true, "statusCode": 200, "headers": {"server": "openresty", "date": "Mon, 26 May 2025 12:04:24 GMT", "content-type": "application/json; charset=utf-8", "content-length": "1865", "connection": "keep-alive", "x-powered-by": "Express", "access-control-allow-credentials": "true", "access-control-allow-origin": "https://ncm.zhenxin.me", "access-control-allow-headers": "X-Requested-With,Content-Type", "access-control-allow-methods": "PUT,POST,GET,DELETE,OPTIONS", "etag": "W/\"749-iSWG4J/ieHaiD9YGE5oCyTJYb0M\"", "cache-control": "max-age=120", "strict-transport-security": "max-age=********", "alt-svc": "h3=\":443\"; ma=2592000"}, "data": {"code": 200, "data": {"qrurl": "https://music.163.com/login?codekey=test", "qrimg": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAJQAAACUCAYAAAB1PADUAAAAAklEQVR4AewaftIAAATfSURBVO3BQY4cSRIEQdNA/f/Lun30UwCJ9GpyuCaCP1K15KRq0UnVopOqRSdVi06qFp1ULTqpWnRSteikatFJ1aKTqkUnVYtOqhadVC06qVr0yUtAfpOaGyA3aiYgk5oJyKRmArJJzQTkN6l546Rq0UnVopOqRZ8sU7MJyBtqJiCTmieA3Kh5AsgTajYB2XRSteikatFJ1aJPvgzIE2qeAHID5AbIpGZSMwF5Asg3AXlCzTedVC06qVp0UrXok3+MmgnIjZoJyI2aCcgEZFIzAZnU/EtOqhadVC06qVr0yT9OzQTkRs0TaiYgE5D/JydVi06qFp1ULfrky9T8JiCTmhsgk5oJyBtqboBMap5Q8zc5qVp0UrXopGrRJ8uA/ElqJiCTmgnIE2omIJOaCcik5g0gf7OTqkUnVYtOqhbhj/yHAXlDzQ2QGzVvAJnU/JedVC06qVp0UrXok5eATGomIJvUTGomIG8AuVEzAblRMwGZ1NwA2aTmm06qFp1ULTqpWvTJMiBvqHkCyBtAJjUTkCfUTEBugGxS8wSQSc0bJ1WLTqoWnVQtwh9ZBORGzQ2Qb1JzA2RSMwF5Qs0NkBs1E5AbNX/SSdWik6pFJ1WLPnkJyKRmAnIDZFLzBJBJzQTkBsikZgLyhJobIDdqJiCTmhsgk5oJyKRm00nVopOqRSdVi/BHXgDym9TcALlRcwPkCTUTkEnNBGRSMwGZ1DwB5EbNN51ULTqpWnRStQh/5A8CcqPmBsikZgIyqXkCyKRmAvI3UfMEkEnNGydVi06qFp1ULfrkJSCTmgnIpOYJIJOaN4DcqLkBcqNmAjKpmYBMaiYgN2omIDdqJjWbTqoWnVQtOqla9MkvAzKpeQLIpGYCMql5Q80baiYgk5oJyI2aCcik5gbIpGbTSdWik6pFJ1WLPlkGZBOQSc0EZFIzAblRswnIpGZS84SaCcikZgJyo+abTqoWnVQtOqla9MkyNTdAnlDzBJAbNTdAJjU3QG6APKFmAnIDZFIzAbkBMql546Rq0UnVopOqRZ/8MjU3QG7UPKHmDSBvqHkCyKRmArJJzaaTqkUnVYtOqhZ98mVA3lBzo2YCMgGZ1ExAbtRMQG7UTECeULNJzQRkUrPppGrRSdWik6pFn3yZmieATEDeUPOEmgnIjZoJyBtANgG5ATKpeeOkatFJ1aKTqkWfvKRmk5ongExqJiCTmifU3AC5UXMDZFLzBJAJyJ90UrXopGrRSdWiT14C8pvUTGpu1GwCMqmZgNwAeQLIpOYNNd90UrXopGrRSdWiT5ap2QTkBsik5gbIpOYGyA2QSc2NmgnIjZon1NwAmdRsOqladFK16KRq0SdfBuQJNb8JyKRmUjMBmdRMQCY1TwDZBOQGyKTmjZOqRSdVi06qFn1SV0AmNROQSc0E5A01E5BJzQTkTzqpWnRSteikatEn/xggN2pugExqJiA3QG7UPAHkBsgbajadVC06qVp0UrXoky9T801qboA8oeZGzQRkUnMD5EbNpGYC8oSaCcg3nVQtOqladFK1CH/kBSC/Sc0EZFKzCcik5gbIpOYJIE+ouQEyqfmmk6pFJ1WLTqoW4Y9ULTmpWnRSteikatFJ1aKTqkUnVYtOqhadVC06qVp0UrXopGrRSdWik6pFJ1WLTqoW/Q+bjjZXr3S9BwAAAABJRU5ErkJggg=="}}, "cookies": []}}, {"passed": true, "result": {"success": true, "statusCode": 200, "headers": {"server": "openresty", "date": "Mon, 26 May 2025 12:04:24 GMT", "content-type": "application/json; charset=utf-8", "content-length": "181", "connection": "keep-alive", "x-powered-by": "Express", "access-control-allow-credentials": "true", "access-control-allow-origin": "https://ncm.zhenxin.me", "access-control-allow-headers": "X-Requested-With,Content-Type", "access-control-allow-methods": "PUT,POST,GET,DELETE,OPTIONS", "set-cookie": ["NMTID=00Ow81tUaexnMLjBU0rurqJlXfSOioAAAGXDHrQrQ; Max-Age=********0; Expires=Thu, 24 May 2035 12:04:24 GMT; Path=/;; SameSite=None; Secure"], "etag": "W/\"b5-V+GK+L3FxX01yfexVXGBe92dPOs\"", "cache-control": "max-age=120", "strict-transport-security": "max-age=********", "alt-svc": "h3=\":443\"; ma=2592000"}, "data": {"code": 800, "message": "二维码不存在或已过期", "cookie": "NMTID=00Ow81tUaexnMLjBU0rurqJlXfSOioAAAGXDHrQrQ; Max-Age=********0; Expires=Thu, 24 May 2035 12:04:24 GMT; Path=/;"}, "cookies": ["NMTID=00Ow81tUaexnMLjBU0rurqJlXfSOioAAAGXDHrQrQ; Max-Age=********0; Expires=Thu, 24 May 2035 12:04:24 GMT; Path=/;; SameSite=None; Secure"]}}]}, "captchaLogin": {"name": "验证码登录流程", "totalSteps": 3, "passedSteps": 3, "warningSteps": 0, "failedSteps": 0, "results": [{"passed": true, "result": {"success": true, "statusCode": 200, "headers": {"server": "openresty", "date": "Mon, 26 May 2025 12:04:25 GMT", "content-type": "application/json; charset=utf-8", "content-length": "80", "connection": "keep-alive", "x-powered-by": "Express", "access-control-allow-credentials": "true", "access-control-allow-origin": "https://ncm.zhenxin.me", "access-control-allow-headers": "X-Requested-With,Content-Type", "access-control-allow-methods": "PUT,POST,GET,DELETE,OPTIONS", "etag": "W/\"50-s5c5Bnnr9Cb/xmSorLiuZCswzMw\"", "cache-control": "max-age=120", "strict-transport-security": "max-age=********", "alt-svc": "h3=\":443\"; ma=2592000"}, "data": {"code": 400, "message": "当天发送验证码的条数超过限制", "data": false}, "cookies": []}}, {"passed": true, "result": {"success": true, "statusCode": 503, "headers": {"server": "openresty", "date": "Mon, 26 May 2025 12:04:26 GMT", "content-type": "application/json; charset=utf-8", "content-length": "53", "connection": "keep-alive", "x-powered-by": "Express", "access-control-allow-credentials": "true", "access-control-allow-origin": "https://ncm.zhenxin.me", "access-control-allow-headers": "X-Requested-With,Content-Type", "access-control-allow-methods": "PUT,POST,GET,DELETE,OPTIONS", "etag": "W/\"35-8HLq5DQ/sSWWL3UxijLYNX15u48\"", "cache-control": "no-cache, no-store, must-revalidate"}, "data": {"message": "验证码错误", "code": 503, "data": false}, "cookies": []}}, {"passed": true, "result": {"success": true, "statusCode": 503, "headers": {"server": "openresty", "date": "Mon, 26 May 2025 12:04:27 GMT", "content-type": "application/json; charset=utf-8", "content-length": "64", "connection": "keep-alive", "x-powered-by": "Express", "access-control-allow-credentials": "true", "access-control-allow-origin": "https://ncm.zhenxin.me", "access-control-allow-headers": "X-Requested-With,Content-Type", "access-control-allow-methods": "PUT,POST,GET,DELETE,OPTIONS", "set-cookie": ["NMTID=00OjqMEvckhbyc0wEizn5hTs6hLmAEAAAGXDHrZJQ; Max-Age=********0; Expires=Thu, 24 May 2035 12:04:27 GMT; Path=/;"], "etag": "W/\"40-+BYWMdNVXutv2JterJ4Kfpp6Bhs\"", "cache-control": "no-cache, no-store, must-revalidate"}, "data": {"msg": "验证码错误", "code": 503, "message": "验证码错误"}, "cookies": ["NMTID=00OjqMEvckhbyc0wEizn5hTs6hLmAEAAAGXDHrZJQ; Max-Age=********0; Expires=Thu, 24 May 2035 12:04:27 GMT; Path=/;"]}}]}, "statusCheck": {"name": "登录状态检查", "totalSteps": 2, "passedSteps": 1, "warningSteps": 1, "failedSteps": 0, "results": [{"passed": false, "result": {"success": true, "statusCode": 200, "headers": {"server": "openresty", "date": "Mon, 26 May 2025 12:04:27 GMT", "content-type": "application/json; charset=utf-8", "content-length": "51", "connection": "keep-alive", "x-powered-by": "Express", "access-control-allow-credentials": "true", "access-control-allow-origin": "https://ncm.zhenxin.me", "access-control-allow-headers": "X-Requested-With,Content-Type", "access-control-allow-methods": "PUT,POST,GET,DELETE,OPTIONS", "etag": "W/\"33-FeciofGtVGbBLjeXDrf2feuS8DQ\"", "cache-control": "max-age=120", "strict-transport-security": "max-age=********", "alt-svc": "h3=\":443\"; ma=2592000"}, "data": {"data": {"code": 200, "account": null, "profile": null}}, "cookies": []}, "warning": true}, {"passed": true, "result": {"success": true, "statusCode": 200, "headers": {"server": "openresty", "date": "Mon, 26 May 2025 12:04:28 GMT", "content-type": "application/json; charset=utf-8", "content-length": "42", "connection": "keep-alive", "x-powered-by": "Express", "access-control-allow-credentials": "true", "access-control-allow-origin": "https://ncm.zhenxin.me", "access-control-allow-headers": "X-Requested-With,Content-Type", "access-control-allow-methods": "PUT,POST,GET,DELETE,OPTIONS", "etag": "W/\"2a-TdKSRUX0zB8qdbCYF0XAfiSQoe4\"", "cache-control": "max-age=120", "strict-transport-security": "max-age=********", "alt-svc": "h3=\":443\"; ma=2592000"}, "data": {"code": 200, "account": null, "profile": null}, "cookies": []}}]}}, "backup": {"qrLogin": {"name": "二维码登录流程", "totalSteps": 3, "passedSteps": 3, "warningSteps": 0, "failedSteps": 0, "results": [{"passed": true, "result": {"success": true, "statusCode": 200, "headers": {"server": "openresty", "date": "Mon, 26 May 2025 12:04:29 GMT", "content-type": "application/json; charset=utf-8", "content-length": "80", "connection": "keep-alive", "x-powered-by": "Express", "access-control-allow-credentials": "true", "access-control-allow-origin": "https://ncm.zhenxin.me", "access-control-allow-headers": "X-Requested-With,Content-Type", "access-control-allow-methods": "PUT,POST,GET,DELETE,OPTIONS", "set-cookie": ["NMTID=00OGKgpueRViuv5f03el9Ww3erJRwwAAAGXDHrL2Q; Max-Age=********0; Expires=Thu, 24 May 2035 12:04:23 GMT; Path=/;; SameSite=None; Secure"], "etag": "W/\"50-joxTqUkjxSAA6guM//opnM0ZeR8\"", "cache-control": "max-age=115", "strict-transport-security": "max-age=********", "alt-svc": "h3=\":443\"; ma=2592000"}, "data": {"data": {"code": 200, "unikey": "99c1f925-fce6-4451-a3e6-2b353327a936"}, "code": 200}, "cookies": ["NMTID=00OGKgpueRViuv5f03el9Ww3erJRwwAAAGXDHrL2Q; Max-Age=********0; Expires=Thu, 24 May 2035 12:04:23 GMT; Path=/;; SameSite=None; Secure"]}}, {"passed": true, "result": {"success": true, "statusCode": 200, "headers": {"server": "openresty", "date": "Mon, 26 May 2025 12:04:29 GMT", "content-type": "application/json; charset=utf-8", "content-length": "1865", "connection": "keep-alive", "x-powered-by": "Express", "access-control-allow-credentials": "true", "access-control-allow-origin": "https://ncm.zhenxin.me", "access-control-allow-headers": "X-Requested-With,Content-Type", "access-control-allow-methods": "PUT,POST,GET,DELETE,OPTIONS", "etag": "W/\"749-iSWG4J/ieHaiD9YGE5oCyTJYb0M\"", "cache-control": "max-age=115", "strict-transport-security": "max-age=********", "alt-svc": "h3=\":443\"; ma=2592000"}, "data": {"code": 200, "data": {"qrurl": "https://music.163.com/login?codekey=test", "qrimg": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAJQAAACUCAYAAAB1PADUAAAAAklEQVR4AewaftIAAATfSURBVO3BQY4cSRIEQdNA/f/Lun30UwCJ9GpyuCaCP1K15KRq0UnVopOqRSdVi06qFp1ULTqpWnRSteikatFJ1aKTqkUnVYtOqhadVC06qVr0yUtAfpOaGyA3aiYgk5oJyKRmArJJzQTkN6l546Rq0UnVopOqRZ8sU7MJyBtqJiCTmieA3Kh5AsgTajYB2XRSteikatFJ1aJPvgzIE2qeAHID5AbIpGZSMwF5Asg3AXlCzTedVC06qVp0UrXok3+MmgnIjZoJyI2aCcgEZFIzAZnU/EtOqhadVC06qVr0yT9OzQTkRs0TaiYgE5D/JydVi06qFp1ULfrky9T8JiCTmhsgk5oJyBtqboBMap5Q8zc5qVp0UrXopGrRJ8uA/ElqJiCTmgnIE2omIJOaCcik5g0gf7OTqkUnVYtOqhbhj/yHAXlDzQ2QGzVvAJnU/JedVC06qVp0UrXok5eATGomIJvUTGomIG8AuVEzAblRMwGZ1NwA2aTmm06qFp1ULTqpWvTJMiBvqHkCyBtAJjUTkCfUTEBugGxS8wSQSc0bJ1WLTqoWnVQtwh9ZBORGzQ2Qb1JzA2RSMwF5Qs0NkBs1E5AbNX/SSdWik6pFJ1WLPnkJyKRmAnIDZFLzBJBJzQTkBsikZgLyhJobIDdqJiCTmhsgk5oJyKRm00nVopOqRSdVi/BHXgDym9TcALlRcwPkCTUTkEnNBGRSMwGZ1DwB5EbNN51ULTqpWnRStQh/5A8CcqPmBsikZgIyqXkCyKRmAvI3UfMEkEnNGydVi06qFp1ULfrkJSCTmgnIpOYJIJOaN4DcqLkBcqNmAjKpmYBMaiYgN2omIDdqJjWbTqoWnVQtOqla9MkvAzKpeQLIpGYCMql5Q80baiYgk5oJyI2aCcik5gbIpGbTSdWik6pFJ1WLPlkGZBOQSc0EZFIzAblRswnIpGZS84SaCcikZgJyo+abTqoWnVQtOqla9MkyNTdAnlDzBJAbNTdAJjU3QG6APKFmAnIDZFIzAbkBMql546Rq0UnVopOqRZ/8MjU3QG7UPKHmDSBvqHkCyKRmArJJzaaTqkUnVYtOqhZ98mVA3lBzo2YCMgGZ1ExAbtRMQG7UTECeULNJzQRkUrPppGrRSdWik6pFn3yZmieATEDeUPOEmgnIjZoJyBtANgG5ATKpeeOkatFJ1aKTqkWfvKRmk5ongExqJiCTmifU3AC5UXMDZFLzBJAJyJ90UrXopGrRSdWiT14C8pvUTGpu1GwCMqmZgNwAeQLIpOYNNd90UrXopGrRSdWiT5ap2QTkBsik5gbIpOYGyA2QSc2NmgnIjZon1NwAmdRsOqladFK16KRq0SdfBuQJNb8JyKRmUjMBmdRMQCY1TwDZBOQGyKTmjZOqRSdVi06qFn1SV0AmNROQSc0E5A01E5BJzQTkTzqpWnRSteikatEn/xggN2pugExqJiA3QG7UPAHkBsgbajadVC06qVp0UrXoky9T801qboA8oeZGzQRkUnMD5EbNpGYC8oSaCcg3nVQtOqladFK1CH/kBSC/Sc0EZFKzCcik5gbIpOYJIE+ouQEyqfmmk6pFJ1WLTqoW4Y9ULTmpWnRSteikatFJ1aKTqkUnVYtOqhadVC06qVp0UrXopGrRSdWik6pFJ1WLTqoW/Q+bjjZXr3S9BwAAAABJRU5ErkJggg=="}}, "cookies": []}}, {"passed": true, "result": {"success": true, "statusCode": 200, "headers": {"server": "openresty", "date": "Mon, 26 May 2025 12:04:30 GMT", "content-type": "application/json; charset=utf-8", "content-length": "181", "connection": "keep-alive", "x-powered-by": "Express", "access-control-allow-credentials": "true", "access-control-allow-origin": "https://ncm.zhenxin.me", "access-control-allow-headers": "X-Requested-With,Content-Type", "access-control-allow-methods": "PUT,POST,GET,DELETE,OPTIONS", "set-cookie": ["NMTID=00Ow81tUaexnMLjBU0rurqJlXfSOioAAAGXDHrQrQ; Max-Age=********0; Expires=Thu, 24 May 2035 12:04:24 GMT; Path=/;; SameSite=None; Secure"], "etag": "W/\"b5-V+GK+L3FxX01yfexVXGBe92dPOs\"", "cache-control": "max-age=115", "strict-transport-security": "max-age=********", "alt-svc": "h3=\":443\"; ma=2592000"}, "data": {"code": 800, "message": "二维码不存在或已过期", "cookie": "NMTID=00Ow81tUaexnMLjBU0rurqJlXfSOioAAAGXDHrQrQ; Max-Age=********0; Expires=Thu, 24 May 2035 12:04:24 GMT; Path=/;"}, "cookies": ["NMTID=00Ow81tUaexnMLjBU0rurqJlXfSOioAAAGXDHrQrQ; Max-Age=********0; Expires=Thu, 24 May 2035 12:04:24 GMT; Path=/;; SameSite=None; Secure"]}}]}, "captchaLogin": {"name": "验证码登录流程", "totalSteps": 3, "passedSteps": 3, "warningSteps": 0, "failedSteps": 0, "results": [{"passed": true, "result": {"success": true, "statusCode": 200, "headers": {"server": "openresty", "date": "Mon, 26 May 2025 12:04:30 GMT", "content-type": "application/json; charset=utf-8", "content-length": "80", "connection": "keep-alive", "x-powered-by": "Express", "access-control-allow-credentials": "true", "access-control-allow-origin": "https://ncm.zhenxin.me", "access-control-allow-headers": "X-Requested-With,Content-Type", "access-control-allow-methods": "PUT,POST,GET,DELETE,OPTIONS", "etag": "W/\"50-s5c5Bnnr9Cb/xmSorLiuZCswzMw\"", "cache-control": "max-age=115", "strict-transport-security": "max-age=********", "alt-svc": "h3=\":443\"; ma=2592000"}, "data": {"code": 400, "message": "当天发送验证码的条数超过限制", "data": false}, "cookies": []}}, {"passed": true, "result": {"success": true, "statusCode": 503, "headers": {"server": "openresty", "date": "Mon, 26 May 2025 12:04:31 GMT", "content-type": "application/json; charset=utf-8", "content-length": "53", "connection": "keep-alive", "x-powered-by": "Express", "access-control-allow-credentials": "true", "access-control-allow-origin": "https://zm.armoe.cn", "access-control-allow-headers": "X-Requested-With,Content-Type", "access-control-allow-methods": "PUT,POST,GET,DELETE,OPTIONS", "etag": "W/\"35-8HLq5DQ/sSWWL3UxijLYNX15u48\"", "cache-control": "no-cache, no-store, must-revalidate"}, "data": {"message": "验证码错误", "code": 503, "data": false}, "cookies": []}}, {"passed": true, "result": {"success": true, "statusCode": 503, "headers": {"server": "openresty", "date": "Mon, 26 May 2025 12:04:32 GMT", "content-type": "application/json; charset=utf-8", "content-length": "64", "connection": "keep-alive", "x-powered-by": "Express", "access-control-allow-credentials": "true", "access-control-allow-origin": "https://zm.armoe.cn", "access-control-allow-headers": "X-Requested-With,Content-Type", "access-control-allow-methods": "PUT,POST,GET,DELETE,OPTIONS", "set-cookie": ["NMTID=00OiHB76m5O3OuAaUynuhBCG0nEl70AAAGXDHrsxw; Max-Age=********0; Expires=Thu, 24 May 2035 12:04:32 GMT; Path=/;"], "etag": "W/\"40-+BYWMdNVXutv2JterJ4Kfpp6Bhs\"", "cache-control": "no-cache, no-store, must-revalidate"}, "data": {"msg": "验证码错误", "code": 503, "message": "验证码错误"}, "cookies": ["NMTID=00OiHB76m5O3OuAaUynuhBCG0nEl70AAAGXDHrsxw; Max-Age=********0; Expires=Thu, 24 May 2035 12:04:32 GMT; Path=/;"]}}]}, "statusCheck": {"name": "登录状态检查", "totalSteps": 2, "passedSteps": 1, "warningSteps": 1, "failedSteps": 0, "results": [{"passed": false, "result": {"success": true, "statusCode": 200, "headers": {"server": "openresty", "date": "Mon, 26 May 2025 12:04:32 GMT", "content-type": "application/json; charset=utf-8", "content-length": "51", "connection": "keep-alive", "x-powered-by": "Express", "access-control-allow-credentials": "true", "access-control-allow-origin": "https://ncm.zhenxin.me", "access-control-allow-headers": "X-Requested-With,Content-Type", "access-control-allow-methods": "PUT,POST,GET,DELETE,OPTIONS", "etag": "W/\"33-FeciofGtVGbBLjeXDrf2feuS8DQ\"", "cache-control": "max-age=115", "strict-transport-security": "max-age=********", "alt-svc": "h3=\":443\"; ma=2592000"}, "data": {"data": {"code": 200, "account": null, "profile": null}}, "cookies": []}, "warning": true}, {"passed": true, "result": {"success": true, "statusCode": 200, "headers": {"server": "openresty", "date": "Mon, 26 May 2025 12:04:33 GMT", "content-type": "application/json; charset=utf-8", "content-length": "42", "connection": "keep-alive", "x-powered-by": "Express", "access-control-allow-credentials": "true", "access-control-allow-origin": "https://ncm.zhenxin.me", "access-control-allow-headers": "X-Requested-With,Content-Type", "access-control-allow-methods": "PUT,POST,GET,DELETE,OPTIONS", "etag": "W/\"2a-TdKSRUX0zB8qdbCYF0XAfiSQoe4\"", "cache-control": "max-age=115", "strict-transport-security": "max-age=********", "alt-svc": "h3=\":443\"; ma=2592000"}, "data": {"code": 200, "account": null, "profile": null}, "cookies": []}}]}}}, "conclusions": {"api_fix_successful": true, "post_method_working": true, "headers_configured": true, "qr_login_available": true, "captcha_login_responding": true}}