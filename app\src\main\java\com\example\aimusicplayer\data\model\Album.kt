package com.example.aimusicplayer.data.model

import com.google.gson.annotations.SerializedName

/**
 * 专辑数据模型
 * 用于统一项目中的专辑数据结构
 */
data class Album(
    var id: String = "",
    var name: String = "",
    var artistId: String = "",
    var artistName: String = "",
    var coverUrl: String = "",

    @SerializedName("picUrl")
    var picUrl: String? = null,

    var description: String = "",
    var songCount: Int = 0,
    var publishTime: String = "",
    var songs: MutableList<Song> = mutableListOf(),
    var artists: List<Artist>? = null
) {
    /**
     * 获取图片URL
     * 兼容方法，优先返回picUrl，如果为空则返回coverUrl
     */
    fun getImageUrl(): String {
        return picUrl ?: coverUrl
    }

    /**
     * 设置图片URL
     * 同时设置coverUrl以保持兼容性
     */
    fun setImageUrl(url: String) {
        picUrl = url
        // 同时设置coverUrl以保持兼容性
        if (coverUrl.isEmpty()) {
            coverUrl = url
        }
    }

    /**
     * 添加歌曲
     */
    fun addSong(song: Song) {
        songs.add(song)
    }
}
